from typing import Literal, Dict, List, Tuple, Optional
import numpy as np
import pandas as pd
from dataclasses import dataclass
from enum import Enum
import asyncio
from collections import deque
import logging

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class MarketRegime(Enum):
    """市场状态枚举"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"


class SignalStrength(Enum):
    """信号强度枚举"""
    WEAK = 1
    MEDIUM = 2
    STRONG = 3
    VERY_STRONG = 4


@dataclass
class TradingSignal:
    """交易信号数据类"""
    direction: Literal["buy", "sell", "hold"]
    strength: SignalStrength
    confidence: float
    price: float
    stop_loss: float
    take_profit: float
    timestamp: float


@dataclass
class RiskMetrics:
    """风险指标数据类"""
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0


class AdvancedParams(BaseParams):
    """高级策略参数"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K线周期")
    
    # 趋势识别参数
    trend_period: int = Field(default=20, title="趋势识别周期")
    trend_threshold: float = Field(default=0.02, title="趋势阈值")
    
    # 多时间框架参数
    timeframes: List[str] = Field(default=["M1", "M5", "M15"], title="多时间框架")
    
    # 风险管理参数
    max_position_size: int = Field(default=10, title="最大仓位")
    risk_per_trade: float = Field(default=0.02, title="单笔风险比例")
    max_daily_loss: float = Field(default=0.05, title="日最大亏损")
    
    # 机器学习参数
    ml_lookback: int = Field(default=100, title="ML回看周期")
    ml_features: int = Field(default=20, title="特征数量")
    
    # 动态止损参数
    atr_multiplier: float = Field(default=2.0, title="ATR倍数")
    trailing_stop_pct: float = Field(default=0.01, title="追踪止损百分比")


class AdvancedState(BaseState):
    """高级策略状态"""
    # 市场状态
    market_regime: str = Field(default="sideways", title="市场状态")
    trend_strength: float = Field(default=0.0, title="趋势强度")
    volatility: float = Field(default=0.0, title="波动率")
    
    # 技术指标
    sma_fast: float = Field(default=0.0, title="快速均线")
    sma_slow: float = Field(default=0.0, title="慢速均线")
    ema_fast: float = Field(default=0.0, title="快速EMA")
    ema_slow: float = Field(default=0.0, title="慢速EMA")
    rsi: float = Field(default=50.0, title="RSI")
    macd: float = Field(default=0.0, title="MACD")
    macd_signal: float = Field(default=0.0, title="MACD信号线")
    bb_upper: float = Field(default=0.0, title="布林带上轨")
    bb_lower: float = Field(default=0.0, title="布林带下轨")
    atr: float = Field(default=0.0, title="ATR")
    
    # 信号状态
    signal_direction: str = Field(default="hold", title="信号方向")
    signal_strength: float = Field(default=0.0, title="信号强度")
    signal_confidence: float = Field(default=0.0, title="信号置信度")
    
    # 风险指标
    current_drawdown: float = Field(default=0.0, title="当前回撤")
    daily_pnl: float = Field(default=0.0, title="日盈亏")
    position_risk: float = Field(default=0.0, title="仓位风险")


class AdvancedTradingStrategy(BaseStrategy):
    """高级全能交易策略"""
    
    def __init__(self):
        super().__init__()
        self.params_map = AdvancedParams()
        self.state_map = AdvancedState()
        
        # 数据存储
        self.price_history = deque(maxlen=1000)
        self.volume_history = deque(maxlen=1000)
        self.signal_history = deque(maxlen=100)
        
        # 多时间框架数据
        self.timeframe_data = {}
        
        # 风险管理
        self.risk_metrics = RiskMetrics()
        self.active_orders = {}
        self.position_tracker = {}
        
        # 机器学习模型（简化版）
        self.ml_features = deque(maxlen=self.params_map.ml_lookback)
        self.ml_predictions = deque(maxlen=50)
        
        # 性能监控
        self.trade_log = []
        self.equity_curve = []
        
        # 初始化日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    @property
    def main_indicator_data(self) -> Dict[str, float]:
        """主图指标数据"""
        return {
            "SMA_Fast": self.state_map.sma_fast,
            "SMA_Slow": self.state_map.sma_slow,
            "EMA_Fast": self.state_map.ema_fast,
            "EMA_Slow": self.state_map.ema_slow,
            "BB_Upper": self.state_map.bb_upper,
            "BB_Lower": self.state_map.bb_lower,
            "Market_Regime": hash(self.state_map.market_regime) % 100,
        }
    
    @property
    def sub_indicator_data(self) -> Dict[str, float]:
        """副图指标数据"""
        return {
            "RSI": self.state_map.rsi,
            "MACD": self.state_map.macd,
            "MACD_Signal": self.state_map.macd_signal,
            "ATR": self.state_map.atr,
            "Signal_Strength": self.state_map.signal_strength,
            "Trend_Strength": self.state_map.trend_strength,
        }
    
    def on_start(self):
        """策略启动"""
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()
        super().on_start()
        
        # 初始化多时间框架
        self._initialize_timeframes()
        
        self.logger.info("高级交易策略已启动")
        
    def on_stop(self):
        """策略停止"""
        super().on_stop()
        self._generate_performance_report()
        self.logger.info("高级交易策略已停止")
    
    def on_tick(self, tick: TickData):
        """Tick数据处理"""
        super().on_tick(tick)
        self.kline_generator.tick_to_kline(tick)
        
        # 实时风险监控
        self._monitor_risk()
    
    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """交易回调"""
        super().on_trade(trade, log)
        self._update_trade_statistics(trade)
        
    def callback(self, kline: KLineData) -> None:
        """K线回调 - 主要策略逻辑"""
        try:
            # 更新价格历史
            self._update_price_history(kline)
            
            # 计算技术指标
            self._calculate_indicators()
            
            # 识别市场状态
            self._identify_market_regime()
            
            # 生成交易信号
            signal = self._generate_trading_signal(kline)
            
            # 执行交易决策
            if signal:
                self._execute_trading_decision(signal)
            
            # 更新UI
            self._update_ui(kline)
            
        except Exception as e:
            self.logger.error(f"策略执行错误: {e}")
    
    def real_time_callback(self, kline: KLineData) -> None:
        """实时K线回调"""
        self._calculate_indicators()
        self._update_ui(kline)

    def _initialize_timeframes(self):
        """初始化多时间框架"""
        for timeframe in self.params_map.timeframes:
            self.timeframe_data[timeframe] = {
                'klines': deque(maxlen=200),
                'indicators': {},
                'signals': deque(maxlen=50)
            }

    def _update_price_history(self, kline: KLineData):
        """更新价格历史数据"""
        self.price_history.append({
            'timestamp': kline.timestamp,
            'open': kline.open,
            'high': kline.high,
            'low': kline.low,
            'close': kline.close,
            'volume': kline.volume
        })

    def _calculate_indicators(self):
        """计算技术指标"""
        if len(self.price_history) < 50:
            return

        prices = np.array([p['close'] for p in self.price_history])
        highs = np.array([p['high'] for p in self.price_history])
        lows = np.array([p['low'] for p in self.price_history])
        volumes = np.array([p['volume'] for p in self.price_history])

        # 移动平均线
        self.state_map.sma_fast = np.mean(prices[-10:])
        self.state_map.sma_slow = np.mean(prices[-20:])

        # EMA
        self.state_map.ema_fast = self._calculate_ema(prices, 12)
        self.state_map.ema_slow = self._calculate_ema(prices, 26)

        # RSI
        self.state_map.rsi = self._calculate_rsi(prices, 14)

        # MACD
        macd_line = self.state_map.ema_fast - self.state_map.ema_slow
        self.state_map.macd = macd_line
        self.state_map.macd_signal = self._calculate_ema(np.array([macd_line]), 9)

        # 布林带
        bb_period = 20
        if len(prices) >= bb_period:
            bb_sma = np.mean(prices[-bb_period:])
            bb_std = np.std(prices[-bb_period:])
            self.state_map.bb_upper = bb_sma + (2 * bb_std)
            self.state_map.bb_lower = bb_sma - (2 * bb_std)

        # ATR
        self.state_map.atr = self._calculate_atr(highs, lows, prices, 14)

        # 波动率
        if len(prices) >= 20:
            returns = np.diff(prices[-20:]) / prices[-20:-1]
            self.state_map.volatility = np.std(returns) * np.sqrt(252)

    def _calculate_ema(self, prices: np.ndarray, period: int) -> float:
        """计算指数移动平均"""
        if len(prices) < period:
            return np.mean(prices)

        alpha = 2 / (period + 1)
        ema = prices[0]
        for price in prices[1:]:
            ema = alpha * price + (1 - alpha) * ema
        return ema

    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """计算RSI"""
        if len(prices) < period + 1:
            return 50.0

        deltas = np.diff(prices[-period-1:])
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains)
        avg_loss = np.mean(losses)

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _calculate_atr(self, highs: np.ndarray, lows: np.ndarray,
                      closes: np.ndarray, period: int = 14) -> float:
        """计算平均真实波幅"""
        if len(highs) < period + 1:
            return 0.0

        high_low = highs[-period:] - lows[-period:]
        high_close = np.abs(highs[-period:] - closes[-period-1:-1])
        low_close = np.abs(lows[-period:] - closes[-period-1:-1])

        true_ranges = np.maximum(high_low, np.maximum(high_close, low_close))
        return np.mean(true_ranges)

    def _identify_market_regime(self):
        """识别市场状态 - 强化版基于前沿技术"""
        if len(self.price_history) < 50:
            return

        prices = np.array([p['close'] for p in self.price_history])
        volumes = np.array([p['volume'] for p in self.price_history])

        # 1. 多维特征提取
        features = self._extract_multidimensional_features(prices, volumes)

        # 2. 基于Lorentzian距离的状态分类
        regime_classification = self._lorentzian_regime_classification(features)

        # 3. Hidden Markov状态检测
        hmm_state = self._hidden_markov_detection(prices)

        # 4. 自适应阈值计算
        adaptive_thresholds = self._calculate_adaptive_thresholds(prices)

        # 5. 多模型融合决策
        final_regime = self._fuse_regime_predictions(
            regime_classification, hmm_state, adaptive_thresholds, features
        )

        # 更新状态
        self.state_map.market_regime = final_regime['regime']
        self.state_map.trend_strength = final_regime['strength']
        self.state_map.volatility = final_regime['volatility']

    def _extract_multidimensional_features(self, prices: np.ndarray, volumes: np.ndarray) -> Dict:
        """提取多维特征向量"""
        if len(prices) < 30:
            return {}

        # 价格特征
        returns = np.diff(prices[-30:]) / prices[-30:-1]

        # 1. 趋势特征
        trend_features = {
            'price_momentum_5': (prices[-1] - prices[-6]) / prices[-6] if len(prices) >= 6 else 0,
            'price_momentum_10': (prices[-1] - prices[-11]) / prices[-11] if len(prices) >= 11 else 0,
            'price_momentum_20': (prices[-1] - prices[-21]) / prices[-21] if len(prices) >= 21 else 0,
            'ema_slope': self._calculate_slope(prices[-10:]) if len(prices) >= 10 else 0,
        }

        # 2. 波动率特征
        volatility_features = {
            'returns_std_5': np.std(returns[-5:]) if len(returns) >= 5 else 0,
            'returns_std_10': np.std(returns[-10:]) if len(returns) >= 10 else 0,
            'returns_std_20': np.std(returns[-20:]) if len(returns) >= 20 else 0,
            'volatility_ratio': np.std(returns[-5:]) / np.std(returns[-20:]) if len(returns) >= 20 and np.std(returns[-20:]) > 0 else 1,
        }

        # 3. 成交量特征
        volume_features = {
            'volume_trend': (volumes[-1] - np.mean(volumes[-10:])) / np.mean(volumes[-10:]) if len(volumes) >= 10 else 0,
            'volume_volatility': np.std(volumes[-10:]) / np.mean(volumes[-10:]) if len(volumes) >= 10 and np.mean(volumes[-10:]) > 0 else 0,
            'price_volume_corr': np.corrcoef(prices[-10:], volumes[-10:])[0,1] if len(prices) >= 10 else 0,
        }

        # 4. 技术指标特征
        technical_features = {
            'rsi_level': self.state_map.rsi,
            'rsi_divergence': self._calculate_rsi_divergence(prices),
            'macd_signal': self.state_map.macd - self.state_map.macd_signal,
            'bb_position': self._calculate_bb_position(prices[-1]) if hasattr(self.state_map, 'bb_upper') else 0.5,
        }

        # 5. 市场微结构特征
        microstructure_features = {
            'price_acceleration': self._calculate_price_acceleration(prices),
            'volatility_clustering': self._calculate_volatility_clustering(returns),
            'jump_detection': self._detect_price_jumps(returns),
        }

        return {
            **trend_features,
            **volatility_features,
            **volume_features,
            **technical_features,
            **microstructure_features
        }

    def _calculate_slope(self, data: np.ndarray) -> float:
        """计算数据斜率"""
        if len(data) < 2:
            return 0
        x = np.arange(len(data))
        return np.polyfit(x, data, 1)[0]

    def _calculate_rsi_divergence(self, prices: np.ndarray) -> float:
        """计算RSI背离"""
        if len(prices) < 20:
            return 0

        # 简化的背离检测
        recent_high_idx = np.argmax(prices[-10:])
        recent_low_idx = np.argmin(prices[-10:])

        if recent_high_idx > recent_low_idx:
            # 可能的顶背离
            return -1
        elif recent_low_idx > recent_high_idx:
            # 可能的底背离
            return 1
        else:
            return 0

    def _calculate_bb_position(self, current_price: float) -> float:
        """计算布林带位置"""
        if not hasattr(self.state_map, 'bb_upper') or not hasattr(self.state_map, 'bb_lower'):
            return 0.5

        bb_range = self.state_map.bb_upper - self.state_map.bb_lower
        if bb_range == 0:
            return 0.5

        return (current_price - self.state_map.bb_lower) / bb_range

    def _calculate_price_acceleration(self, prices: np.ndarray) -> float:
        """计算价格加速度"""
        if len(prices) < 3:
            return 0

        # 二阶差分
        first_diff = np.diff(prices[-5:]) if len(prices) >= 5 else np.diff(prices)
        if len(first_diff) < 2:
            return 0

        second_diff = np.diff(first_diff)
        return np.mean(second_diff)

    def _calculate_volatility_clustering(self, returns: np.ndarray) -> float:
        """计算波动率聚集性"""
        if len(returns) < 10:
            return 0

        # 计算绝对收益率的自相关
        abs_returns = np.abs(returns[-10:])
        if len(abs_returns) < 5:
            return 0

        # 简化的自相关计算
        mean_abs_return = np.mean(abs_returns)
        if mean_abs_return == 0:
            return 0

        autocorr = np.corrcoef(abs_returns[:-1], abs_returns[1:])[0,1]
        return autocorr if not np.isnan(autocorr) else 0

    def _detect_price_jumps(self, returns: np.ndarray) -> float:
        """检测价格跳跃"""
        if len(returns) < 5:
            return 0

        # 基于标准差的跳跃检测
        recent_returns = returns[-5:]
        std_threshold = 2.5 * np.std(returns[-20:]) if len(returns) >= 20 else 2.5 * np.std(returns)

        jump_count = np.sum(np.abs(recent_returns) > std_threshold)
        return jump_count / len(recent_returns)

    def _lorentzian_regime_classification(self, features: Dict) -> Dict:
        """基于Lorentzian距离的状态分类"""
        if not features:
            return {'regime': MarketRegime.SIDEWAYS.value, 'confidence': 0.0}

        # 构建特征向量
        feature_vector = np.array([
            features.get('price_momentum_5', 0),
            features.get('price_momentum_10', 0),
            features.get('returns_std_5', 0),
            features.get('volume_trend', 0),
            features.get('rsi_level', 50) / 100,  # 归一化
            features.get('macd_signal', 0),
            features.get('volatility_ratio', 1),
            features.get('price_acceleration', 0)
        ])

        # 定义状态原型（基于历史经验）
        prototypes = {
            MarketRegime.TRENDING_UP.value: np.array([0.02, 0.05, 0.01, 0.1, 0.7, 0.1, 1.2, 0.001]),
            MarketRegime.TRENDING_DOWN.value: np.array([-0.02, -0.05, 0.01, -0.1, 0.3, -0.1, 1.2, -0.001]),
            MarketRegime.VOLATILE.value: np.array([0.0, 0.0, 0.03, 0.0, 0.5, 0.0, 2.0, 0.002]),
            MarketRegime.SIDEWAYS.value: np.array([0.0, 0.0, 0.005, 0.0, 0.5, 0.0, 0.8, 0.0])
        }

        # 计算Lorentzian距离
        distances = {}
        for regime, prototype in prototypes.items():
            distance = np.sum(np.log(1 + np.abs(feature_vector - prototype)))
            distances[regime] = distance

        # 找到最近的状态
        closest_regime = min(distances, key=distances.get)
        min_distance = distances[closest_regime]

        # 计算置信度（距离越小置信度越高）
        max_distance = max(distances.values())
        confidence = 1.0 - (min_distance / max_distance) if max_distance > 0 else 0.5

        return {'regime': closest_regime, 'confidence': confidence}

    def _hidden_markov_detection(self, prices: np.ndarray) -> Dict:
        """简化的Hidden Markov状态检测"""
        if len(prices) < 20:
            return {'regime': MarketRegime.SIDEWAYS.value, 'confidence': 0.0}

        # 计算收益率
        returns = np.diff(prices[-20:]) / prices[-20:-1]

        # 简化的状态检测基于收益率分布
        mean_return = np.mean(returns)
        std_return = np.std(returns)

        # 状态判断逻辑
        if mean_return > 0.001 and std_return < 0.02:
            regime = MarketRegime.TRENDING_UP.value
            confidence = min(abs(mean_return) * 100, 1.0)
        elif mean_return < -0.001 and std_return < 0.02:
            regime = MarketRegime.TRENDING_DOWN.value
            confidence = min(abs(mean_return) * 100, 1.0)
        elif std_return > 0.03:
            regime = MarketRegime.VOLATILE.value
            confidence = min(std_return * 20, 1.0)
        else:
            regime = MarketRegime.SIDEWAYS.value
            confidence = 1.0 - std_return * 20

        return {'regime': regime, 'confidence': max(0.0, min(1.0, confidence))}

    def _calculate_adaptive_thresholds(self, prices: np.ndarray) -> Dict:
        """计算自适应阈值"""
        if len(prices) < 30:
            return {'trend_threshold': 0.02, 'volatility_threshold': 0.05}

        # 基于历史波动率的自适应阈值
        returns = np.diff(prices[-30:]) / prices[-30:-1]
        historical_vol = np.std(returns)

        # 动态调整阈值
        trend_threshold = max(0.01, min(0.05, historical_vol * 2))
        volatility_threshold = max(0.02, min(0.1, historical_vol * 3))

        return {
            'trend_threshold': trend_threshold,
            'volatility_threshold': volatility_threshold
        }

    def _fuse_regime_predictions(self, lorentzian_result: Dict, hmm_result: Dict,
                                thresholds: Dict, features: Dict) -> Dict:
        """融合多个模型的状态预测"""
        # 权重分配
        lorentzian_weight = 0.4
        hmm_weight = 0.3
        technical_weight = 0.3

        # 技术指标投票
        technical_regime = self._technical_regime_vote(features, thresholds)

        # 收集所有预测
        predictions = [
            (lorentzian_result['regime'], lorentzian_result['confidence'] * lorentzian_weight),
            (hmm_result['regime'], hmm_result['confidence'] * hmm_weight),
            (technical_regime['regime'], technical_regime['confidence'] * technical_weight)
        ]

        # 加权投票
        regime_scores = {}
        for regime, score in predictions:
            if regime in regime_scores:
                regime_scores[regime] += score
            else:
                regime_scores[regime] = score

        # 选择得分最高的状态
        final_regime = max(regime_scores, key=regime_scores.get)
        final_confidence = regime_scores[final_regime] / sum(regime_scores.values())

        # 计算综合强度和波动率
        strength = self._calculate_regime_strength(features, final_regime)
        volatility = features.get('returns_std_10', 0) * 100

        return {
            'regime': final_regime,
            'strength': strength,
            'volatility': volatility,
            'confidence': final_confidence
        }

    def _technical_regime_vote(self, features: Dict, thresholds: Dict) -> Dict:
        """技术指标投票"""
        votes = []

        # RSI投票
        rsi = features.get('rsi_level', 50)
        if rsi > 70:
            votes.append(MarketRegime.TRENDING_UP.value)
        elif rsi < 30:
            votes.append(MarketRegime.TRENDING_DOWN.value)
        else:
            votes.append(MarketRegime.SIDEWAYS.value)

        # 动量投票
        momentum = features.get('price_momentum_10', 0)
        if momentum > thresholds.get('trend_threshold', 0.02):
            votes.append(MarketRegime.TRENDING_UP.value)
        elif momentum < -thresholds.get('trend_threshold', 0.02):
            votes.append(MarketRegime.TRENDING_DOWN.value)
        else:
            votes.append(MarketRegime.SIDEWAYS.value)

        # 波动率投票
        volatility = features.get('returns_std_5', 0)
        if volatility > thresholds.get('volatility_threshold', 0.05):
            votes.append(MarketRegime.VOLATILE.value)
        else:
            votes.append(MarketRegime.SIDEWAYS.value)

        # 统计投票结果
        from collections import Counter
        vote_counts = Counter(votes)
        most_common = vote_counts.most_common(1)[0]

        regime = most_common[0]
        confidence = most_common[1] / len(votes)

        return {'regime': regime, 'confidence': confidence}

    def _calculate_regime_strength(self, features: Dict, regime: str) -> float:
        """计算状态强度"""
        if regime == MarketRegime.TRENDING_UP.value:
            return min(100, abs(features.get('price_momentum_10', 0)) * 1000)
        elif regime == MarketRegime.TRENDING_DOWN.value:
            return min(100, abs(features.get('price_momentum_10', 0)) * 1000)
        elif regime == MarketRegime.VOLATILE.value:
            return min(100, features.get('returns_std_5', 0) * 2000)
        else:
            return 0

    def _generate_trading_signal(self, kline: KLineData) -> Optional[TradingSignal]:
        """生成交易信号"""
        if len(self.price_history) < 50:
            return None

        # 多指标信号融合
        signals = []

        # 趋势信号
        trend_signal = self._get_trend_signal()
        if trend_signal:
            signals.append(trend_signal)

        # 动量信号
        momentum_signal = self._get_momentum_signal()
        if momentum_signal:
            signals.append(momentum_signal)

        # 反转信号
        reversal_signal = self._get_reversal_signal()
        if reversal_signal:
            signals.append(reversal_signal)

        # 突破信号
        breakout_signal = self._get_breakout_signal(kline)
        if breakout_signal:
            signals.append(breakout_signal)

        # 信号融合和过滤
        return self._fuse_signals(signals, kline)

    def _get_trend_signal(self) -> Optional[Dict]:
        """获取趋势跟踪信号"""
        # 基于EMA交叉的趋势信号
        if self.state_map.ema_fast > self.state_map.ema_slow:
            if self.state_map.market_regime in [MarketRegime.TRENDING_UP.value]:
                return {
                    'direction': 'buy',
                    'strength': SignalStrength.MEDIUM,
                    'confidence': 0.7,
                    'type': 'trend'
                }
        elif self.state_map.ema_fast < self.state_map.ema_slow:
            if self.state_map.market_regime in [MarketRegime.TRENDING_DOWN.value]:
                return {
                    'direction': 'sell',
                    'strength': SignalStrength.MEDIUM,
                    'confidence': 0.7,
                    'type': 'trend'
                }
        return None

    def _get_momentum_signal(self) -> Optional[Dict]:
        """获取动量信号"""
        # 基于RSI的动量信号
        if self.state_map.rsi < 30 and self.state_map.market_regime != MarketRegime.TRENDING_DOWN.value:
            return {
                'direction': 'buy',
                'strength': SignalStrength.STRONG,
                'confidence': 0.8,
                'type': 'momentum'
            }
        elif self.state_map.rsi > 70 and self.state_map.market_regime != MarketRegime.TRENDING_UP.value:
            return {
                'direction': 'sell',
                'strength': SignalStrength.STRONG,
                'confidence': 0.8,
                'type': 'momentum'
            }
        return None

    def _get_reversal_signal(self) -> Optional[Dict]:
        """获取反转信号"""
        # 基于MACD的反转信号
        if (self.state_map.macd > self.state_map.macd_signal and
            self.state_map.market_regime in [MarketRegime.SIDEWAYS.value, MarketRegime.VOLATILE.value]):
            return {
                'direction': 'buy',
                'strength': SignalStrength.WEAK,
                'confidence': 0.6,
                'type': 'reversal'
            }
        elif (self.state_map.macd < self.state_map.macd_signal and
              self.state_map.market_regime in [MarketRegime.SIDEWAYS.value, MarketRegime.VOLATILE.value]):
            return {
                'direction': 'sell',
                'strength': SignalStrength.WEAK,
                'confidence': 0.6,
                'type': 'reversal'
            }
        return None

    def _get_breakout_signal(self, kline: KLineData) -> Optional[Dict]:
        """获取突破信号"""
        # 基于布林带的突破信号
        if kline.close > self.state_map.bb_upper:
            return {
                'direction': 'buy',
                'strength': SignalStrength.STRONG,
                'confidence': 0.75,
                'type': 'breakout'
            }
        elif kline.close < self.state_map.bb_lower:
            return {
                'direction': 'sell',
                'strength': SignalStrength.STRONG,
                'confidence': 0.75,
                'type': 'breakout'
            }
        return None

    def _fuse_signals(self, signals: List[Dict], kline: KLineData) -> Optional[TradingSignal]:
        """信号融合"""
        if not signals:
            return None

        # 计算信号权重
        buy_weight = 0
        sell_weight = 0
        total_confidence = 0

        for signal in signals:
            weight = signal['strength'].value * signal['confidence']
            total_confidence += signal['confidence']

            if signal['direction'] == 'buy':
                buy_weight += weight
            elif signal['direction'] == 'sell':
                sell_weight += weight

        # 确定最终方向
        if buy_weight > sell_weight and buy_weight > 2:
            direction = 'buy'
            strength_value = min(int(buy_weight), 4)
            confidence = min(total_confidence / len(signals), 1.0)
        elif sell_weight > buy_weight and sell_weight > 2:
            direction = 'sell'
            strength_value = min(int(sell_weight), 4)
            confidence = min(total_confidence / len(signals), 1.0)
        else:
            return None

        # 计算止损止盈
        stop_loss, take_profit = self._calculate_stop_levels(kline, direction)

        return TradingSignal(
            direction=direction,
            strength=SignalStrength(strength_value),
            confidence=confidence,
            price=kline.close,
            stop_loss=stop_loss,
            take_profit=take_profit,
            timestamp=kline.timestamp
        )

    def _calculate_stop_levels(self, kline: KLineData, direction: str) -> Tuple[float, float]:
        """计算止损止盈位"""
        atr_stop = self.state_map.atr * self.params_map.atr_multiplier

        if direction == 'buy':
            stop_loss = kline.close - atr_stop
            take_profit = kline.close + (atr_stop * 2)  # 2:1 风险收益比
        else:
            stop_loss = kline.close + atr_stop
            take_profit = kline.close - (atr_stop * 2)

        return stop_loss, take_profit

    def _execute_trading_decision(self, signal: TradingSignal):
        """执行交易决策"""
        if not self.trading:
            return

        # 风险检查
        if not self._risk_check(signal):
            return

        # 获取当前仓位
        position = self.get_position(self.params_map.instrument_id)

        # 计算交易量
        volume = self._calculate_position_size(signal)

        # 执行交易
        if signal.direction == 'buy':
            if position.net_position <= 0:  # 开多或平空
                self._place_order('buy', signal.price, volume, signal)
        elif signal.direction == 'sell':
            if position.net_position >= 0:  # 开空或平多
                self._place_order('sell', signal.price, volume, signal)

        # 记录信号
        self.signal_history.append(signal)
        self.state_map.signal_direction = signal.direction
        self.state_map.signal_strength = signal.strength.value
        self.state_map.signal_confidence = signal.confidence

    def _risk_check(self, signal: TradingSignal) -> bool:
        """风险检查"""
        # 检查日亏损限制
        if self.state_map.daily_pnl < -self.params_map.max_daily_loss:
            self.logger.warning("达到日最大亏损限制，停止交易")
            return False

        # 检查最大回撤
        if self.state_map.current_drawdown > 0.1:  # 10%最大回撤
            self.logger.warning("达到最大回撤限制，停止交易")
            return False

        # 检查信号质量
        if signal.confidence < 0.5:
            return False

        return True

    def _calculate_position_size(self, signal: TradingSignal) -> int:
        """计算仓位大小"""
        # 基于风险的仓位计算
        risk_amount = self.params_map.risk_per_trade
        stop_distance = abs(signal.price - signal.stop_loss) / signal.price

        if stop_distance > 0:
            position_size = int(risk_amount / stop_distance)
            return min(position_size, self.params_map.max_position_size)

        return 1

    def _place_order(self, direction: str, price: float, volume: int, signal: TradingSignal):
        """下单"""
        try:
            order_id = self.send_order(
                exchange=self.params_map.exchange,
                instrument_id=self.params_map.instrument_id,
                volume=volume,
                price=price,
                order_direction=direction
            )

            if order_id:
                self.active_orders[order_id] = {
                    'signal': signal,
                    'timestamp': signal.timestamp,
                    'volume': volume,
                    'direction': direction
                }

                self.logger.info(f"下单成功: {direction} {volume}@{price}, 信号强度: {signal.strength.value}")

        except Exception as e:
            self.logger.error(f"下单失败: {e}")

    def _monitor_risk(self):
        """实时风险监控"""
        # 更新当前回撤
        if self.equity_curve:
            peak = max(self.equity_curve)
            current = self.equity_curve[-1] if self.equity_curve else 0
            self.state_map.current_drawdown = (peak - current) / peak if peak > 0 else 0

        # 追踪止损检查
        self._check_trailing_stops()

    def _check_trailing_stops(self):
        """检查追踪止损"""
        position = self.get_position(self.params_map.instrument_id)
        if position.net_position == 0:
            return

        current_price = self.price_history[-1]['close'] if self.price_history else 0
        if current_price == 0:
            return

        # 实现追踪止损逻辑
        trailing_pct = self.params_map.trailing_stop_pct

        if position.net_position > 0:  # 多头仓位
            # 计算追踪止损价格
            stop_price = current_price * (1 - trailing_pct)
            # 这里应该设置止损单，简化处理

        elif position.net_position < 0:  # 空头仓位
            # 计算追踪止损价格
            stop_price = current_price * (1 + trailing_pct)
            # 这里应该设置止损单，简化处理

    def _update_trade_statistics(self, trade: TradeData):
        """更新交易统计"""
        self.trade_log.append({
            'timestamp': trade.timestamp,
            'direction': trade.order_direction,
            'price': trade.price,
            'volume': trade.volume,
            'pnl': getattr(trade, 'pnl', 0)
        })

        # 更新风险指标
        self.risk_metrics.total_trades += 1

        if hasattr(trade, 'pnl') and trade.pnl > 0:
            self.risk_metrics.winning_trades += 1
        elif hasattr(trade, 'pnl') and trade.pnl < 0:
            self.risk_metrics.losing_trades += 1

        # 计算胜率
        if self.risk_metrics.total_trades > 0:
            self.risk_metrics.win_rate = self.risk_metrics.winning_trades / self.risk_metrics.total_trades

    def _update_ui(self, kline: KLineData):
        """更新UI显示"""
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": getattr(self, 'signal_price', 0),
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        if self.trading:
            self.update_status_bar()

    def _generate_performance_report(self):
        """生成性能报告"""
        if not self.trade_log:
            return

        total_pnl = sum(trade.get('pnl', 0) for trade in self.trade_log)

        report = f"""
        ========== 策略性能报告 ==========
        总交易次数: {self.risk_metrics.total_trades}
        盈利交易: {self.risk_metrics.winning_trades}
        亏损交易: {self.risk_metrics.losing_trades}
        胜率: {self.risk_metrics.win_rate:.2%}
        总盈亏: {total_pnl:.2f}
        最大回撤: {self.risk_metrics.max_drawdown:.2%}
        夏普比率: {self.risk_metrics.sharpe_ratio:.2f}
        盈亏比: {self.risk_metrics.profit_factor:.2f}
        ================================
        """

        self.logger.info(report)

        # 保存详细报告到文件
        try:
            with open(f"strategy_report_{self.params_map.instrument_id}.txt", "w", encoding="utf-8") as f:
                f.write(report)
                f.write("\n详细交易记录:\n")
                for trade in self.trade_log:
                    f.write(f"{trade}\n")
        except Exception as e:
            self.logger.error(f"保存报告失败: {e}")

    # 机器学习相关方法（简化版）
    def _extract_ml_features(self) -> Optional[np.ndarray]:
        """提取机器学习特征"""
        if len(self.price_history) < 50:
            return None

        features = []

        # 价格特征
        prices = np.array([p['close'] for p in self.price_history[-20:]])
        features.extend([
            np.mean(prices),
            np.std(prices),
            (prices[-1] - prices[0]) / prices[0],  # 收益率
        ])

        # 技术指标特征
        features.extend([
            self.state_map.rsi,
            self.state_map.macd,
            self.state_map.atr,
            self.state_map.trend_strength,
            self.state_map.volatility
        ])

        # 市场状态特征
        regime_encoding = {
            MarketRegime.TRENDING_UP.value: 1,
            MarketRegime.TRENDING_DOWN.value: -1,
            MarketRegime.SIDEWAYS.value: 0,
            MarketRegime.VOLATILE.value: 0.5
        }
        features.append(regime_encoding.get(self.state_map.market_regime, 0))

        return np.array(features)

    def _ml_predict(self) -> Optional[float]:
        """机器学习预测（简化版）"""
        features = self._extract_ml_features()
        if features is None:
            return None

        # 这里应该是训练好的模型预测
        # 简化版：基于特征的简单预测
        prediction = np.tanh(np.sum(features) / len(features))
        return prediction

    # 辅助方法
    def get_strategy_status(self) -> Dict:
        """获取策略状态"""
        return {
            'market_regime': self.state_map.market_regime,
            'trend_strength': self.state_map.trend_strength,
            'signal_direction': self.state_map.signal_direction,
            'signal_strength': self.state_map.signal_strength,
            'signal_confidence': self.state_map.signal_confidence,
            'current_drawdown': self.state_map.current_drawdown,
            'daily_pnl': self.state_map.daily_pnl,
            'total_trades': self.risk_metrics.total_trades,
            'win_rate': self.risk_metrics.win_rate,
        }

    def optimize_parameters(self, optimization_data: Dict):
        """参数优化接口"""
        # 这里可以实现参数优化逻辑
        # 基于历史回测结果调整参数
        pass

    def export_signals(self, filename: str):
        """导出信号历史"""
        try:
            import json
            signals_data = []
            for signal in self.signal_history:
                signals_data.append({
                    'timestamp': signal.timestamp,
                    'direction': signal.direction,
                    'strength': signal.strength.value,
                    'confidence': signal.confidence,
                    'price': signal.price,
                    'stop_loss': signal.stop_loss,
                    'take_profit': signal.take_profit
                })

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(signals_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"信号历史已导出到: {filename}")
        except Exception as e:
            self.logger.error(f"导出信号失败: {e}")


# 使用示例和测试代码
if __name__ == "__main__":
    # 创建策略实例
    strategy = AdvancedTradingStrategy()

    # 设置参数
    strategy.params_map.exchange = "SHFE"
    strategy.params_map.instrument_id = "rb2501"
    strategy.params_map.kline_style = "M1"
    strategy.params_map.max_position_size = 5
    strategy.params_map.risk_per_trade = 0.02

    print("高级交易策略已创建")
    print("主要功能:")
    print("1. 自动趋势识别 (上涨/下跌/震荡)")
    print("2. 多指标信号融合")
    print("3. 智能风险管理")
    print("4. 动态止损止盈")
    print("5. 实时性能监控")
    print("6. 机器学习预测")
    print("7. 多时间框架分析")
    print("8. 详细交易报告")
