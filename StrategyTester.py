"""
高级交易策略测试和优化工具
用于回测、参数优化和性能分析
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import json
from datetime import datetime, timedelta
import asyncio
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

from AdvancedTradingStrategy import AdvancedTradingStrategy, MarketRegime, SignalStrength


@dataclass
class BacktestResult:
    """回测结果数据类"""
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    avg_trade_duration: float
    volatility: float
    calmar_ratio: float
    equity_curve: List[float]
    trade_log: List[Dict]


class StrategyTester:
    """策略测试器"""
    
    def __init__(self, strategy: AdvancedTradingStrategy):
        self.strategy = strategy
        self.test_data = None
        self.results = []
        
    def load_test_data(self, data_path: str = None, generate_synthetic: bool = True):
        """加载测试数据"""
        if generate_synthetic:
            self.test_data = self._generate_synthetic_data()
        else:
            # 从文件加载真实数据
            try:
                self.test_data = pd.read_csv(data_path)
            except Exception as e:
                print(f"加载数据失败: {e}")
                self.test_data = self._generate_synthetic_data()
    
    def _generate_synthetic_data(self, days: int = 30, freq: str = '1min') -> pd.DataFrame:
        """生成合成测试数据"""
        # 生成时间序列
        start_date = datetime.now() - timedelta(days=days)
        date_range = pd.date_range(start=start_date, periods=days*24*60, freq=freq)
        
        # 生成价格数据（几何布朗运动 + 趋势 + 噪声）
        np.random.seed(42)
        n_points = len(date_range)
        
        # 基础价格走势
        base_price = 4000
        drift = 0.0001  # 日漂移率
        volatility = 0.02  # 波动率
        
        # 添加趋势变化
        trend_changes = np.random.choice([0, 1], size=n_points//1440, p=[0.8, 0.2])  # 每天20%概率趋势变化
        trend_periods = np.repeat(trend_changes, 1440)[:n_points]
        
        # 生成价格序列
        returns = np.random.normal(drift, volatility, n_points)
        
        # 添加趋势影响
        for i in range(1, n_points):
            if trend_periods[i]:
                returns[i] += np.random.choice([-0.001, 0.001], p=[0.5, 0.5])
        
        # 计算价格
        prices = base_price * np.exp(np.cumsum(returns))
        
        # 生成OHLCV数据
        data = []
        for i in range(n_points):
            open_price = prices[i]
            close_price = prices[i] * (1 + np.random.normal(0, 0.001))
            high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, 0.002)))
            low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, 0.002)))
            volume = np.random.randint(100, 1000)
            
            data.append({
                'timestamp': date_range[i],
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume
            })
        
        return pd.DataFrame(data)
    
    def run_backtest(self, start_capital: float = 100000) -> BacktestResult:
        """运行回测"""
        if self.test_data is None:
            raise ValueError("请先加载测试数据")
        
        # 初始化回测环境
        capital = start_capital
        position = 0
        equity_curve = [capital]
        trade_log = []
        
        # 模拟策略运行
        for i, row in self.test_data.iterrows():
            # 创建模拟K线数据
            kline_data = self._create_mock_kline(row)
            
            # 更新策略数据
            self.strategy._update_price_history(kline_data)
            
            if len(self.strategy.price_history) < 50:
                continue
            
            # 计算指标
            self.strategy._calculate_indicators()
            
            # 识别市场状态
            self.strategy._identify_market_regime()
            
            # 生成信号
            signal = self.strategy._generate_trading_signal(kline_data)
            
            if signal and signal.confidence > 0.6:
                # 模拟交易执行
                trade_result = self._simulate_trade(signal, capital, position, row['close'])
                
                if trade_result:
                    capital = trade_result['new_capital']
                    position = trade_result['new_position']
                    trade_log.append(trade_result['trade_record'])
            
            equity_curve.append(capital)
        
        # 计算回测结果
        return self._calculate_backtest_metrics(equity_curve, trade_log, start_capital)
    
    def _create_mock_kline(self, row) -> object:
        """创建模拟K线对象"""
        class MockKLine:
            def __init__(self, data):
                self.timestamp = data['timestamp'].timestamp()
                self.open = data['open']
                self.high = data['high']
                self.low = data['low']
                self.close = data['close']
                self.volume = data['volume']
        
        return MockKLine(row)
    
    def _simulate_trade(self, signal, capital: float, current_position: int, 
                       current_price: float) -> Optional[Dict]:
        """模拟交易执行"""
        # 简化的交易模拟
        trade_size = min(int(capital * 0.1 / current_price), 10)  # 最大10%资金或10手
        
        if signal.direction == 'buy' and current_position <= 0:
            # 买入
            cost = trade_size * current_price
            if cost <= capital:
                new_position = current_position + trade_size
                new_capital = capital - cost
                
                return {
                    'new_capital': new_capital,
                    'new_position': new_position,
                    'trade_record': {
                        'timestamp': signal.timestamp,
                        'direction': 'buy',
                        'price': current_price,
                        'size': trade_size,
                        'signal_strength': signal.strength.value,
                        'confidence': signal.confidence
                    }
                }
        
        elif signal.direction == 'sell' and current_position >= 0:
            # 卖出
            if current_position > 0:
                # 平多
                proceeds = min(current_position, trade_size) * current_price
                new_position = current_position - min(current_position, trade_size)
                new_capital = capital + proceeds
            else:
                # 开空（简化处理）
                new_position = current_position - trade_size
                new_capital = capital  # 简化：不考虑保证金
            
            return {
                'new_capital': new_capital,
                'new_position': new_position,
                'trade_record': {
                    'timestamp': signal.timestamp,
                    'direction': 'sell',
                    'price': current_price,
                    'size': trade_size,
                    'signal_strength': signal.strength.value,
                    'confidence': signal.confidence
                }
            }
        
        return None
    
    def _calculate_backtest_metrics(self, equity_curve: List[float], 
                                  trade_log: List[Dict], 
                                  start_capital: float) -> BacktestResult:
        """计算回测指标"""
        if len(equity_curve) < 2:
            return BacktestResult(0, 0, 0, 0, 0, 0, 0, 0, 0, equity_curve, trade_log)
        
        # 总收益率
        total_return = (equity_curve[-1] - start_capital) / start_capital
        
        # 计算日收益率
        daily_returns = np.diff(equity_curve) / equity_curve[:-1]
        
        # 夏普比率
        if len(daily_returns) > 0 and np.std(daily_returns) > 0:
            sharpe_ratio = np.mean(daily_returns) / np.std(daily_returns) * np.sqrt(252)
        else:
            sharpe_ratio = 0
        
        # 最大回撤
        peak = np.maximum.accumulate(equity_curve)
        drawdown = (peak - equity_curve) / peak
        max_drawdown = np.max(drawdown) if len(drawdown) > 0 else 0
        
        # 交易统计
        total_trades = len(trade_log)
        if total_trades > 0:
            # 简化的盈亏计算
            profits = []
            losses = []
            
            for i in range(1, len(trade_log)):
                if trade_log[i]['direction'] != trade_log[i-1]['direction']:
                    # 计算盈亏
                    pnl = (trade_log[i]['price'] - trade_log[i-1]['price']) * trade_log[i-1]['size']
                    if trade_log[i-1]['direction'] == 'sell':
                        pnl = -pnl
                    
                    if pnl > 0:
                        profits.append(pnl)
                    else:
                        losses.append(abs(pnl))
            
            win_rate = len(profits) / (len(profits) + len(losses)) if (len(profits) + len(losses)) > 0 else 0
            profit_factor = sum(profits) / sum(losses) if sum(losses) > 0 else float('inf')
        else:
            win_rate = 0
            profit_factor = 0
        
        # 波动率
        volatility = np.std(daily_returns) * np.sqrt(252) if len(daily_returns) > 0 else 0
        
        # 卡尔玛比率
        calmar_ratio = total_return / max_drawdown if max_drawdown > 0 else 0
        
        return BacktestResult(
            total_return=total_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            profit_factor=profit_factor,
            total_trades=total_trades,
            avg_trade_duration=0,  # 简化
            volatility=volatility,
            calmar_ratio=calmar_ratio,
            equity_curve=equity_curve,
            trade_log=trade_log
        )
    
    def optimize_parameters(self, param_ranges: Dict) -> Dict:
        """参数优化"""
        best_params = None
        best_score = -float('inf')
        optimization_results = []
        
        # 网格搜索（简化版）
        for trend_threshold in param_ranges.get('trend_threshold', [0.01, 0.02, 0.03]):
            for risk_per_trade in param_ranges.get('risk_per_trade', [0.01, 0.02, 0.03]):
                for atr_multiplier in param_ranges.get('atr_multiplier', [1.5, 2.0, 2.5]):
                    
                    # 设置参数
                    self.strategy.params_map.trend_threshold = trend_threshold
                    self.strategy.params_map.risk_per_trade = risk_per_trade
                    self.strategy.params_map.atr_multiplier = atr_multiplier
                    
                    # 运行回测
                    result = self.run_backtest()
                    
                    # 计算综合评分
                    score = self._calculate_optimization_score(result)
                    
                    optimization_results.append({
                        'params': {
                            'trend_threshold': trend_threshold,
                            'risk_per_trade': risk_per_trade,
                            'atr_multiplier': atr_multiplier
                        },
                        'score': score,
                        'result': result
                    })
                    
                    if score > best_score:
                        best_score = score
                        best_params = {
                            'trend_threshold': trend_threshold,
                            'risk_per_trade': risk_per_trade,
                            'atr_multiplier': atr_multiplier
                        }
        
        return {
            'best_params': best_params,
            'best_score': best_score,
            'all_results': optimization_results
        }
    
    def _calculate_optimization_score(self, result: BacktestResult) -> float:
        """计算优化评分"""
        # 综合评分函数
        return (
            result.total_return * 0.3 +
            result.sharpe_ratio * 0.3 +
            (1 - result.max_drawdown) * 0.2 +
            result.win_rate * 0.2
        )

    def plot_results(self, result: BacktestResult, save_path: str = None):
        """绘制回测结果"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 资金曲线
        axes[0, 0].plot(result.equity_curve)
        axes[0, 0].set_title('资金曲线')
        axes[0, 0].set_xlabel('时间')
        axes[0, 0].set_ylabel('资金')
        axes[0, 0].grid(True)

        # 回撤曲线
        peak = np.maximum.accumulate(result.equity_curve)
        drawdown = (peak - result.equity_curve) / peak
        axes[0, 1].fill_between(range(len(drawdown)), drawdown, alpha=0.3, color='red')
        axes[0, 1].set_title(f'回撤曲线 (最大回撤: {result.max_drawdown:.2%})')
        axes[0, 1].set_xlabel('时间')
        axes[0, 1].set_ylabel('回撤')
        axes[0, 1].grid(True)

        # 交易分布
        if result.trade_log:
            trade_prices = [trade['price'] for trade in result.trade_log]
            axes[1, 0].hist(trade_prices, bins=20, alpha=0.7)
            axes[1, 0].set_title('交易价格分布')
            axes[1, 0].set_xlabel('价格')
            axes[1, 0].set_ylabel('频次')
            axes[1, 0].grid(True)

        # 性能指标
        metrics_text = f"""
        总收益率: {result.total_return:.2%}
        夏普比率: {result.sharpe_ratio:.2f}
        最大回撤: {result.max_drawdown:.2%}
        胜率: {result.win_rate:.2%}
        盈亏比: {result.profit_factor:.2f}
        总交易次数: {result.total_trades}
        波动率: {result.volatility:.2%}
        卡尔玛比率: {result.calmar_ratio:.2f}
        """

        axes[1, 1].text(0.1, 0.5, metrics_text, transform=axes[1, 1].transAxes,
                        fontsize=10, verticalalignment='center',
                        bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.5))
        axes[1, 1].set_title('性能指标')
        axes[1, 1].axis('off')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()

    def generate_report(self, result: BacktestResult, filename: str = None):
        """生成详细报告"""
        report = f"""
        ==================== 策略回测报告 ====================

        回测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

        === 收益指标 ===
        总收益率: {result.total_return:.2%}
        年化收益率: {result.total_return * 252 / len(result.equity_curve):.2%}

        === 风险指标 ===
        最大回撤: {result.max_drawdown:.2%}
        波动率: {result.volatility:.2%}
        夏普比率: {result.sharpe_ratio:.2f}
        卡尔玛比率: {result.calmar_ratio:.2f}

        === 交易指标 ===
        总交易次数: {result.total_trades}
        胜率: {result.win_rate:.2%}
        盈亏比: {result.profit_factor:.2f}
        平均交易持续时间: {result.avg_trade_duration:.2f}

        === 策略参数 ===
        趋势阈值: {self.strategy.params_map.trend_threshold}
        单笔风险: {self.strategy.params_map.risk_per_trade:.2%}
        ATR倍数: {self.strategy.params_map.atr_multiplier}
        最大仓位: {self.strategy.params_map.max_position_size}

        =====================================================
        """

        if filename:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)
                f.write("\n\n=== 详细交易记录 ===\n")
                for i, trade in enumerate(result.trade_log):
                    f.write(f"交易 {i+1}: {trade}\n")

        return report


class StrategyComparison:
    """策略对比工具"""

    def __init__(self):
        self.strategies = {}
        self.results = {}

    def add_strategy(self, name: str, strategy: AdvancedTradingStrategy):
        """添加策略"""
        self.strategies[name] = strategy

    def run_comparison(self, test_data: pd.DataFrame) -> Dict:
        """运行策略对比"""
        comparison_results = {}

        for name, strategy in self.strategies.items():
            print(f"正在测试策略: {name}")

            tester = StrategyTester(strategy)
            tester.test_data = test_data
            result = tester.run_backtest()

            comparison_results[name] = result

        self.results = comparison_results
        return comparison_results

    def plot_comparison(self, save_path: str = None):
        """绘制策略对比图"""
        if not self.results:
            print("请先运行策略对比")
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 资金曲线对比
        for name, result in self.results.items():
            axes[0, 0].plot(result.equity_curve, label=name)
        axes[0, 0].set_title('资金曲线对比')
        axes[0, 0].legend()
        axes[0, 0].grid(True)

        # 收益率对比
        returns = [result.total_return for result in self.results.values()]
        names = list(self.results.keys())
        axes[0, 1].bar(names, returns)
        axes[0, 1].set_title('总收益率对比')
        axes[0, 1].set_ylabel('收益率')

        # 夏普比率对比
        sharpe_ratios = [result.sharpe_ratio for result in self.results.values()]
        axes[1, 0].bar(names, sharpe_ratios)
        axes[1, 0].set_title('夏普比率对比')
        axes[1, 0].set_ylabel('夏普比率')

        # 最大回撤对比
        max_drawdowns = [result.max_drawdown for result in self.results.values()]
        axes[1, 1].bar(names, max_drawdowns)
        axes[1, 1].set_title('最大回撤对比')
        axes[1, 1].set_ylabel('最大回撤')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()


# 使用示例
if __name__ == "__main__":
    print("=== 高级交易策略测试工具 ===")

    # 创建策略实例
    strategy = AdvancedTradingStrategy()

    # 设置测试参数
    strategy.params_map.exchange = "SHFE"
    strategy.params_map.instrument_id = "rb2501"
    strategy.params_map.trend_threshold = 0.02
    strategy.params_map.risk_per_trade = 0.02
    strategy.params_map.atr_multiplier = 2.0

    # 创建测试器
    tester = StrategyTester(strategy)

    # 加载测试数据
    print("正在生成测试数据...")
    tester.load_test_data(generate_synthetic=True)

    # 运行回测
    print("正在运行回测...")
    result = tester.run_backtest(start_capital=100000)

    # 显示结果
    print("\n=== 回测结果 ===")
    print(f"总收益率: {result.total_return:.2%}")
    print(f"夏普比率: {result.sharpe_ratio:.2f}")
    print(f"最大回撤: {result.max_drawdown:.2%}")
    print(f"胜率: {result.win_rate:.2%}")
    print(f"总交易次数: {result.total_trades}")

    # 生成报告
    report = tester.generate_report(result, "backtest_report.txt")
    print("\n报告已保存到 backtest_report.txt")

    # 参数优化示例
    print("\n正在进行参数优化...")
    param_ranges = {
        'trend_threshold': [0.01, 0.02, 0.03],
        'risk_per_trade': [0.01, 0.02, 0.03],
        'atr_multiplier': [1.5, 2.0, 2.5]
    }

    optimization_result = tester.optimize_parameters(param_ranges)
    print(f"最优参数: {optimization_result['best_params']}")
    print(f"最优评分: {optimization_result['best_score']:.4f}")

    print("\n测试完成！")
