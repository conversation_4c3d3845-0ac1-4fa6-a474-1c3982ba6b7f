"""
下一代交易策略 - 基于TradingView和MT5社区最新前沿技术
集成Lorentzian分类、Hidden Markov模型、自适应参数系统
"""

from typing import Literal, Dict, List, Tuple, Optional, Union
import numpy as np
import pandas as pd
from dataclasses import dataclass
from enum import Enum
import asyncio
from collections import deque
import logging
from scipy.spatial.distance import cdist
from scipy.stats import norm
import warnings
warnings.filterwarnings('ignore')

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class MarketRegime(Enum):
    """市场状态枚举 - 基于Hidden Markov模型"""
    BULL_TREND = "bull_trend"          # 牛市趋势
    BEAR_TREND = "bear_trend"          # 熊市趋势
    SIDEWAYS_LOW_VOL = "sideways_low"  # 低波动震荡
    SIDEWAYS_HIGH_VOL = "sideways_high" # 高波动震荡
    TRANSITION = "transition"          # 过渡状态


class SignalType(Enum):
    """信号类型枚举"""
    LORENTZIAN_BUY = "lorentzian_buy"
    LORENTZIAN_SELL = "lorentzian_sell"
    ADAPTIVE_TREND = "adaptive_trend"
    REGIME_SWITCH = "regime_switch"
    NEURAL_PREDICTION = "neural_prediction"


@dataclass
class LorentzianFeature:
    """Lorentzian特征向量"""
    rsi: float
    cci: float
    adx: float
    wt: float
    ema_ratio: float
    volume_ratio: float
    volatility: float
    momentum: float


@dataclass
class MarketState:
    """市场状态数据"""
    regime: MarketRegime
    confidence: float
    transition_probability: float
    volatility_regime: str
    trend_strength: float


class NextGenParams(BaseParams):
    """下一代策略参数"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K线周期")
    
    # Lorentzian分类器参数
    lorentzian_neighbors: int = Field(default=8, title="近邻数量")
    lorentzian_lookback: int = Field(default=100, title="历史回看期")
    feature_count: int = Field(default=8, title="特征数量")
    
    # Hidden Markov模型参数
    hmm_states: int = Field(default=5, title="HMM状态数")
    hmm_lookback: int = Field(default=200, title="HMM训练期")
    
    # 自适应参数
    adaptation_period: int = Field(default=50, title="自适应周期")
    adaptation_rate: float = Field(default=0.1, title="自适应学习率")
    
    # 神经网络参数
    nn_hidden_size: int = Field(default=16, title="神经网络隐藏层")
    nn_lookback: int = Field(default=20, title="神经网络回看期")
    
    # 风险管理参数
    max_position_size: int = Field(default=10, title="最大仓位")
    risk_per_trade: float = Field(default=0.02, title="单笔风险比例")
    adaptive_stop_multiplier: float = Field(default=2.0, title="自适应止损倍数")


class NextGenState(BaseState):
    """下一代策略状态"""
    # 市场状态
    current_regime: str = Field(default="transition", title="当前市场状态")
    regime_confidence: float = Field(default=0.0, title="状态置信度")
    transition_prob: float = Field(default=0.0, title="转换概率")
    
    # Lorentzian分类结果
    lorentzian_signal: str = Field(default="hold", title="Lorentzian信号")
    lorentzian_confidence: float = Field(default=0.0, title="分类置信度")
    nearest_neighbors: int = Field(default=0, title="最近邻数量")
    
    # 自适应参数
    adaptive_atr_mult: float = Field(default=2.0, title="自适应ATR倍数")
    adaptive_rsi_upper: float = Field(default=70.0, title="自适应RSI上限")
    adaptive_rsi_lower: float = Field(default=30.0, title="自适应RSI下限")
    
    # 神经网络预测
    nn_prediction: float = Field(default=0.0, title="神经网络预测")
    nn_confidence: float = Field(default=0.0, title="预测置信度")
    
    # 技术指标
    rsi: float = Field(default=50.0, title="RSI")
    cci: float = Field(default=0.0, title="CCI")
    adx: float = Field(default=0.0, title="ADX")
    atr: float = Field(default=0.0, title="ATR")
    ema_fast: float = Field(default=0.0, title="快速EMA")
    ema_slow: float = Field(default=0.0, title="慢速EMA")


class LorentzianClassifier:
    """Lorentzian分类器 - 基于TradingView最佳实践"""
    
    def __init__(self, neighbors: int = 8, lookback: int = 100):
        self.neighbors = neighbors
        self.lookback = lookback
        self.feature_history = deque(maxlen=lookback)
        self.label_history = deque(maxlen=lookback)
        
    def extract_features(self, data: Dict) -> LorentzianFeature:
        """提取Lorentzian特征向量"""
        return LorentzianFeature(
            rsi=data.get('rsi', 50.0),
            cci=data.get('cci', 0.0),
            adx=data.get('adx', 0.0),
            wt=data.get('wt', 0.0),
            ema_ratio=data.get('ema_ratio', 1.0),
            volume_ratio=data.get('volume_ratio', 1.0),
            volatility=data.get('volatility', 0.0),
            momentum=data.get('momentum', 0.0)
        )
    
    def lorentzian_distance(self, x1: np.ndarray, x2: np.ndarray) -> float:
        """计算Lorentzian距离"""
        return np.sum(np.log(1 + np.abs(x1 - x2)))
    
    def classify(self, current_features: LorentzianFeature) -> Tuple[str, float]:
        """Lorentzian分类预测"""
        if len(self.feature_history) < self.neighbors:
            return "hold", 0.0
        
        # 转换为numpy数组
        current_vector = np.array([
            current_features.rsi, current_features.cci, current_features.adx,
            current_features.wt, current_features.ema_ratio, current_features.volume_ratio,
            current_features.volatility, current_features.momentum
        ])
        
        # 计算与历史特征的距离
        distances = []
        labels = []
        
        for i, (hist_features, hist_label) in enumerate(zip(self.feature_history, self.label_history)):
            hist_vector = np.array([
                hist_features.rsi, hist_features.cci, hist_features.adx,
                hist_features.wt, hist_features.ema_ratio, hist_features.volume_ratio,
                hist_features.volatility, hist_features.momentum
            ])
            
            distance = self.lorentzian_distance(current_vector, hist_vector)
            distances.append(distance)
            labels.append(hist_label)
        
        # 找到最近的k个邻居
        nearest_indices = np.argsort(distances)[:self.neighbors]
        nearest_labels = [labels[i] for i in nearest_indices]
        
        # 投票决定分类结果
        buy_votes = sum(1 for label in nearest_labels if label == 'buy')
        sell_votes = sum(1 for label in nearest_labels if label == 'sell')
        
        if buy_votes > sell_votes:
            confidence = buy_votes / self.neighbors
            return "buy", confidence
        elif sell_votes > buy_votes:
            confidence = sell_votes / self.neighbors
            return "sell", confidence
        else:
            return "hold", 0.5
    
    def update(self, features: LorentzianFeature, label: str):
        """更新历史数据"""
        self.feature_history.append(features)
        self.label_history.append(label)


class HiddenMarkovRegimeDetector:
    """隐马尔可夫市场状态检测器"""
    
    def __init__(self, n_states: int = 5, lookback: int = 200):
        self.n_states = n_states
        self.lookback = lookback
        self.returns_history = deque(maxlen=lookback)
        self.volatility_history = deque(maxlen=lookback)
        
        # 简化的HMM参数
        self.transition_matrix = np.ones((n_states, n_states)) / n_states
        self.emission_means = np.linspace(-0.02, 0.02, n_states)
        self.emission_stds = np.linspace(0.005, 0.03, n_states)
        self.state_probs = np.ones(n_states) / n_states
        
    def update_observations(self, price_return: float, volatility: float):
        """更新观测数据"""
        self.returns_history.append(price_return)
        self.volatility_history.append(volatility)
    
    def detect_regime(self) -> MarketState:
        """检测当前市场状态"""
        if len(self.returns_history) < 20:
            return MarketState(
                regime=MarketRegime.TRANSITION,
                confidence=0.0,
                transition_probability=0.0,
                volatility_regime="unknown",
                trend_strength=0.0
            )
        
        # 计算当前观测的似然
        current_return = self.returns_history[-1]
        current_vol = self.volatility_history[-1]
        
        # 更新状态概率
        likelihoods = []
        for i in range(self.n_states):
            likelihood = norm.pdf(current_return, self.emission_means[i], self.emission_stds[i])
            likelihoods.append(likelihood)
        
        likelihoods = np.array(likelihoods)
        likelihoods = likelihoods / np.sum(likelihoods)  # 归一化
        
        # 更新状态概率
        self.state_probs = self.state_probs @ self.transition_matrix * likelihoods
        self.state_probs = self.state_probs / np.sum(self.state_probs)
        
        # 确定当前最可能的状态
        most_likely_state = np.argmax(self.state_probs)
        confidence = self.state_probs[most_likely_state]
        
        # 映射到市场状态
        regime_mapping = {
            0: MarketRegime.BEAR_TREND,
            1: MarketRegime.SIDEWAYS_LOW_VOL,
            2: MarketRegime.TRANSITION,
            3: MarketRegime.SIDEWAYS_HIGH_VOL,
            4: MarketRegime.BULL_TREND
        }
        
        regime = regime_mapping.get(most_likely_state, MarketRegime.TRANSITION)
        
        # 计算转换概率
        transition_prob = 1.0 - confidence
        
        # 判断波动率状态
        recent_vol = np.mean(list(self.volatility_history)[-10:])
        vol_regime = "high" if recent_vol > np.mean(list(self.volatility_history)) else "low"
        
        # 计算趋势强度
        recent_returns = list(self.returns_history)[-20:]
        trend_strength = abs(np.mean(recent_returns)) / (np.std(recent_returns) + 1e-8)
        
        return MarketState(
            regime=regime,
            confidence=confidence,
            transition_probability=transition_prob,
            volatility_regime=vol_regime,
            trend_strength=trend_strength
        )


class AdaptiveParameterSystem:
    """自适应参数系统"""
    
    def __init__(self, adaptation_rate: float = 0.1):
        self.adaptation_rate = adaptation_rate
        self.performance_history = deque(maxlen=100)
        self.parameter_history = deque(maxlen=100)
        
    def adapt_parameters(self, current_params: Dict, performance_score: float, 
                        market_state: MarketState) -> Dict:
        """根据表现和市场状态自适应调整参数"""
        adapted_params = current_params.copy()
        
        # 记录历史
        self.performance_history.append(performance_score)
        self.parameter_history.append(current_params.copy())
        
        if len(self.performance_history) < 10:
            return adapted_params
        
        # 根据市场状态调整参数
        if market_state.regime == MarketRegime.BULL_TREND:
            # 牛市：降低止损，提高RSI阈值
            adapted_params['atr_multiplier'] = max(1.5, current_params.get('atr_multiplier', 2.0) * 0.9)
            adapted_params['rsi_upper'] = min(80, current_params.get('rsi_upper', 70) + 2)
            
        elif market_state.regime == MarketRegime.BEAR_TREND:
            # 熊市：提高止损，降低RSI阈值
            adapted_params['atr_multiplier'] = min(3.0, current_params.get('atr_multiplier', 2.0) * 1.1)
            adapted_params['rsi_lower'] = max(20, current_params.get('rsi_lower', 30) - 2)
            
        elif market_state.volatility_regime == "high":
            # 高波动：增加止损距离
            adapted_params['atr_multiplier'] = min(3.5, current_params.get('atr_multiplier', 2.0) * 1.2)
            
        # 基于表现调整
        recent_performance = np.mean(list(self.performance_history)[-10:])
        if recent_performance < 0:
            # 表现不佳：更保守的参数
            adapted_params['risk_per_trade'] = max(0.01, current_params.get('risk_per_trade', 0.02) * 0.9)
        
        return adapted_params


class LightweightNeuralNetwork:
    """轻量级神经网络预测器"""
    
    def __init__(self, input_size: int = 10, hidden_size: int = 16):
        self.input_size = input_size
        self.hidden_size = hidden_size
        
        # 简化的权重初始化
        self.W1 = np.random.randn(input_size, hidden_size) * 0.1
        self.b1 = np.zeros(hidden_size)
        self.W2 = np.random.randn(hidden_size, 1) * 0.1
        self.b2 = np.zeros(1)
        
        self.learning_rate = 0.001
        
    def sigmoid(self, x):
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def tanh(self, x):
        return np.tanh(x)
    
    def forward(self, X):
        """前向传播"""
        self.z1 = np.dot(X, self.W1) + self.b1
        self.a1 = self.tanh(self.z1)
        self.z2 = np.dot(self.a1, self.W2) + self.b2
        self.a2 = self.sigmoid(self.z2)
        return self.a2
    
    def predict(self, features: np.ndarray) -> Tuple[float, float]:
        """预测价格方向和置信度"""
        if features.ndim == 1:
            features = features.reshape(1, -1)
        
        prediction = self.forward(features)[0, 0]
        
        # 转换为方向和置信度
        if prediction > 0.6:
            direction = 1.0  # 上涨
            confidence = (prediction - 0.5) * 2
        elif prediction < 0.4:
            direction = -1.0  # 下跌
            confidence = (0.5 - prediction) * 2
        else:
            direction = 0.0  # 横盘
            confidence = 1.0 - abs(prediction - 0.5) * 2
        
        return direction, confidence
    
    def simple_update(self, features: np.ndarray, target: float):
        """简化的权重更新"""
        if features.ndim == 1:
            features = features.reshape(1, -1)
        
        # 前向传播
        prediction = self.forward(features)
        
        # 简化的反向传播
        error = target - prediction
        
        # 更新权重（简化版）
        self.W2 += self.learning_rate * np.dot(self.a1.T, error)
        self.b2 += self.learning_rate * error.sum()
        
        hidden_error = error * self.W2.T * (1 - self.a1**2)  # tanh导数
        self.W1 += self.learning_rate * np.dot(features.T, hidden_error)
        self.b1 += self.learning_rate * hidden_error.sum(axis=0)


class NextGenTradingStrategy(BaseStrategy):
    """下一代交易策略 - 集成前沿技术"""

    def __init__(self):
        super().__init__()
        self.params_map = NextGenParams()
        self.state_map = NextGenState()

        # 核心组件
        self.lorentzian_classifier = LorentzianClassifier(
            neighbors=self.params_map.lorentzian_neighbors,
            lookback=self.params_map.lorentzian_lookback
        )

        self.hmm_detector = HiddenMarkovRegimeDetector(
            n_states=self.params_map.hmm_states,
            lookback=self.params_map.hmm_lookback
        )

        self.adaptive_system = AdaptiveParameterSystem(
            adaptation_rate=self.params_map.adaptation_rate
        )

        self.neural_network = LightweightNeuralNetwork(
            input_size=self.params_map.nn_lookback,
            hidden_size=self.params_map.nn_hidden_size
        )

        # 数据存储
        self.price_history = deque(maxlen=500)
        self.volume_history = deque(maxlen=500)
        self.feature_history = deque(maxlen=200)
        self.performance_history = deque(maxlen=100)

        # 当前市场状态
        self.current_market_state = None
        self.last_signal_time = 0
        self.signal_cooldown = 5  # 信号冷却期（分钟）

        # 自适应参数
        self.adaptive_params = {
            'atr_multiplier': 2.0,
            'rsi_upper': 70.0,
            'rsi_lower': 30.0,
            'risk_per_trade': 0.02
        }

        # 日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    @property
    def main_indicator_data(self) -> Dict[str, float]:
        """主图指标数据"""
        return {
            "EMA_Fast": self.state_map.ema_fast,
            "EMA_Slow": self.state_map.ema_slow,
            "Regime": hash(self.state_map.current_regime) % 100,
            "Lorentzian_Signal": hash(self.state_map.lorentzian_signal) % 100,
            "Adaptive_ATR": self.state_map.adaptive_atr_mult * 10,
        }

    @property
    def sub_indicator_data(self) -> Dict[str, float]:
        """副图指标数据"""
        return {
            "RSI": self.state_map.rsi,
            "CCI": self.state_map.cci,
            "ADX": self.state_map.adx,
            "Lorentzian_Confidence": self.state_map.lorentzian_confidence * 100,
            "Regime_Confidence": self.state_map.regime_confidence * 100,
            "NN_Prediction": self.state_map.nn_prediction * 100,
            "NN_Confidence": self.state_map.nn_confidence * 100,
        }

    def on_start(self):
        """策略启动"""
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()
        super().on_start()

        self.logger.info("下一代交易策略已启动")
        self.logger.info(f"集成组件: Lorentzian分类器, HMM状态检测, 自适应参数, 神经网络预测")

    def on_stop(self):
        """策略停止"""
        super().on_stop()
        self._generate_performance_report()
        self.logger.info("下一代交易策略已停止")

    def on_tick(self, tick: TickData):
        """Tick数据处理"""
        super().on_tick(tick)
        self.kline_generator.tick_to_kline(tick)

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """交易回调"""
        super().on_trade(trade, log)
        self._update_performance_metrics(trade)

    def callback(self, kline: KLineData) -> None:
        """K线回调 - 主要策略逻辑"""
        try:
            # 更新价格历史
            self._update_price_history(kline)

            if len(self.price_history) < 50:
                return

            # 计算技术指标
            self._calculate_indicators()

            # 提取特征向量
            features = self._extract_features()

            # Hidden Markov状态检测
            self._update_market_regime()

            # Lorentzian分类
            self._update_lorentzian_classification(features)

            # 神经网络预测
            self._update_neural_prediction()

            # 自适应参数调整
            self._adapt_parameters()

            # 生成交易信号
            signal = self._generate_next_gen_signal(kline)

            # 执行交易决策
            if signal:
                self._execute_next_gen_decision(signal)

            # 更新UI
            self._update_ui(kline)

        except Exception as e:
            self.logger.error(f"策略执行错误: {e}")
            import traceback
            traceback.print_exc()

    def real_time_callback(self, kline: KLineData) -> None:
        """实时K线回调"""
        self._calculate_indicators()
        self._update_ui(kline)

    def _update_price_history(self, kline: KLineData):
        """更新价格历史"""
        price_data = {
            'timestamp': kline.timestamp,
            'open': kline.open,
            'high': kline.high,
            'low': kline.low,
            'close': kline.close,
            'volume': kline.volume
        }
        self.price_history.append(price_data)
        self.volume_history.append(kline.volume)

    def _calculate_indicators(self):
        """计算技术指标"""
        if len(self.price_history) < 30:
            return

        prices = np.array([p['close'] for p in self.price_history])
        highs = np.array([p['high'] for p in self.price_history])
        lows = np.array([p['low'] for p in self.price_history])
        volumes = np.array([p['volume'] for p in self.price_history])

        # EMA
        self.state_map.ema_fast = self._calculate_ema(prices, 12)
        self.state_map.ema_slow = self._calculate_ema(prices, 26)

        # RSI
        self.state_map.rsi = self._calculate_rsi(prices, 14)

        # CCI
        self.state_map.cci = self._calculate_cci(highs, lows, prices, 20)

        # ADX
        self.state_map.adx = self._calculate_adx(highs, lows, prices, 14)

        # ATR
        self.state_map.atr = self._calculate_atr(highs, lows, prices, 14)

    def _calculate_ema(self, prices: np.ndarray, period: int) -> float:
        """计算EMA"""
        if len(prices) < period:
            return np.mean(prices)

        alpha = 2 / (period + 1)
        ema = prices[0]
        for price in prices[1:]:
            ema = alpha * price + (1 - alpha) * ema
        return ema

    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """计算RSI"""
        if len(prices) < period + 1:
            return 50.0

        deltas = np.diff(prices[-period-1:])
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains)
        avg_loss = np.mean(losses)

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _calculate_cci(self, highs: np.ndarray, lows: np.ndarray,
                      closes: np.ndarray, period: int = 20) -> float:
        """计算CCI"""
        if len(closes) < period:
            return 0.0

        typical_prices = (highs[-period:] + lows[-period:] + closes[-period:]) / 3
        sma_tp = np.mean(typical_prices)
        mean_deviation = np.mean(np.abs(typical_prices - sma_tp))

        if mean_deviation == 0:
            return 0.0

        cci = (typical_prices[-1] - sma_tp) / (0.015 * mean_deviation)
        return cci

    def _calculate_adx(self, highs: np.ndarray, lows: np.ndarray,
                      closes: np.ndarray, period: int = 14) -> float:
        """计算ADX"""
        if len(closes) < period + 1:
            return 0.0

        # 计算True Range
        tr1 = highs[1:] - lows[1:]
        tr2 = np.abs(highs[1:] - closes[:-1])
        tr3 = np.abs(lows[1:] - closes[:-1])
        tr = np.maximum(tr1, np.maximum(tr2, tr3))

        # 计算方向移动
        dm_plus = np.where((highs[1:] - highs[:-1]) > (lows[:-1] - lows[1:]),
                          np.maximum(highs[1:] - highs[:-1], 0), 0)
        dm_minus = np.where((lows[:-1] - lows[1:]) > (highs[1:] - highs[:-1]),
                           np.maximum(lows[:-1] - lows[1:], 0), 0)

        # 平滑处理
        if len(tr) >= period:
            atr = np.mean(tr[-period:])
            adm_plus = np.mean(dm_plus[-period:])
            adm_minus = np.mean(dm_minus[-period:])

            if atr == 0:
                return 0.0

            di_plus = 100 * adm_plus / atr
            di_minus = 100 * adm_minus / atr

            if di_plus + di_minus == 0:
                return 0.0

            dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus)
            return dx

        return 0.0

    def _calculate_atr(self, highs: np.ndarray, lows: np.ndarray,
                      closes: np.ndarray, period: int = 14) -> float:
        """计算ATR"""
        if len(closes) < period + 1:
            return 0.0

        tr1 = highs[1:] - lows[1:]
        tr2 = np.abs(highs[1:] - closes[:-1])
        tr3 = np.abs(lows[1:] - closes[:-1])
        tr = np.maximum(tr1, np.maximum(tr2, tr3))

        return np.mean(tr[-period:])

    def _extract_features(self) -> LorentzianFeature:
        """提取Lorentzian特征向量"""
        if len(self.price_history) < 30:
            return LorentzianFeature(0, 0, 0, 0, 1, 1, 0, 0)

        prices = np.array([p['close'] for p in self.price_history])
        volumes = np.array([p['volume'] for p in self.price_history])

        # 计算特征
        ema_ratio = self.state_map.ema_fast / self.state_map.ema_slow if self.state_map.ema_slow != 0 else 1.0
        volume_ratio = volumes[-1] / np.mean(volumes[-20:]) if len(volumes) >= 20 else 1.0

        # 波动率
        returns = np.diff(prices[-20:]) / prices[-20:-1] if len(prices) >= 20 else np.array([0])
        volatility = np.std(returns) if len(returns) > 1 else 0.0

        # 动量
        momentum = (prices[-1] - prices[-10]) / prices[-10] if len(prices) >= 10 else 0.0

        # Williams %R (作为WT的替代)
        if len(self.price_history) >= 14:
            highs = np.array([p['high'] for p in self.price_history])
            lows = np.array([p['low'] for p in self.price_history])
            highest_high = np.max(highs[-14:])
            lowest_low = np.min(lows[-14:])
            if highest_high != lowest_low:
                wt = -100 * (highest_high - prices[-1]) / (highest_high - lowest_low)
            else:
                wt = -50
        else:
            wt = -50

        return LorentzianFeature(
            rsi=self.state_map.rsi,
            cci=self.state_map.cci,
            adx=self.state_map.adx,
            wt=wt,
            ema_ratio=ema_ratio,
            volume_ratio=volume_ratio,
            volatility=volatility,
            momentum=momentum
        )

    def _update_market_regime(self):
        """更新市场状态"""
        if len(self.price_history) < 20:
            return

        prices = np.array([p['close'] for p in self.price_history])

        # 计算收益率和波动率
        returns = np.diff(prices[-20:]) / prices[-20:-1]
        current_return = returns[-1] if len(returns) > 0 else 0.0
        volatility = np.std(returns) if len(returns) > 1 else 0.0

        # 更新HMM检测器
        self.hmm_detector.update_observations(current_return, volatility)

        # 检测市场状态
        market_state = self.hmm_detector.detect_regime()
        self.current_market_state = market_state

        # 更新状态
        self.state_map.current_regime = market_state.regime.value
        self.state_map.regime_confidence = market_state.confidence
        self.state_map.transition_prob = market_state.transition_probability

    def _update_lorentzian_classification(self, features: LorentzianFeature):
        """更新Lorentzian分类"""
        # 分类预测
        signal, confidence = self.lorentzian_classifier.classify(features)

        self.state_map.lorentzian_signal = signal
        self.state_map.lorentzian_confidence = confidence
        self.state_map.nearest_neighbors = self.lorentzian_classifier.neighbors

        # 生成训练标签（基于未来价格变化）
        if len(self.price_history) >= 5:
            current_price = self.price_history[-1]['close']
            future_prices = [p['close'] for p in list(self.price_history)[-5:]]
            avg_future_price = np.mean(future_prices)

            if avg_future_price > current_price * 1.001:
                label = 'buy'
            elif avg_future_price < current_price * 0.999:
                label = 'sell'
            else:
                label = 'hold'

            # 更新分类器
            if len(self.feature_history) > 0:
                self.lorentzian_classifier.update(self.feature_history[-1], label)

        # 保存当前特征
        self.feature_history.append(features)

    def _update_neural_prediction(self):
        """更新神经网络预测"""
        if len(self.price_history) < self.params_map.nn_lookback:
            return

        # 准备输入特征
        prices = np.array([p['close'] for p in self.price_history])
        returns = np.diff(prices[-self.params_map.nn_lookback-1:]) / prices[-self.params_map.nn_lookback-1:-1]

        if len(returns) >= self.params_map.nn_lookback:
            features = returns[-self.params_map.nn_lookback:]

            # 预测
            direction, confidence = self.neural_network.predict(features)

            self.state_map.nn_prediction = direction
            self.state_map.nn_confidence = confidence

            # 简单的在线学习
            if len(self.price_history) >= self.params_map.nn_lookback + 5:
                # 使用5期后的实际价格变化作为目标
                future_return = (prices[-1] - prices[-6]) / prices[-6]
                target = 1.0 if future_return > 0.001 else (0.0 if future_return < -0.001 else 0.5)

                # 更新网络
                past_features = np.diff(prices[-self.params_map.nn_lookback-6:-5]) / prices[-self.params_map.nn_lookback-6:-6]
                if len(past_features) == self.params_map.nn_lookback:
                    self.neural_network.simple_update(past_features, target)

    def _adapt_parameters(self):
        """自适应参数调整"""
        if self.current_market_state is None:
            return

        # 计算近期表现评分
        performance_score = self._calculate_performance_score()

        # 自适应调整
        self.adaptive_params = self.adaptive_system.adapt_parameters(
            self.adaptive_params, performance_score, self.current_market_state
        )

        # 更新状态
        self.state_map.adaptive_atr_mult = self.adaptive_params.get('atr_multiplier', 2.0)
        self.state_map.adaptive_rsi_upper = self.adaptive_params.get('rsi_upper', 70.0)
        self.state_map.adaptive_rsi_lower = self.adaptive_params.get('rsi_lower', 30.0)

    def _calculate_performance_score(self) -> float:
        """计算性能评分"""
        if len(self.performance_history) < 5:
            return 0.0

        recent_performance = list(self.performance_history)[-10:]
        return np.mean(recent_performance)

    def _generate_next_gen_signal(self, kline: KLineData) -> Optional[Dict]:
        """生成下一代交易信号"""
        # 信号冷却期检查
        current_time = kline.timestamp
        if current_time - self.last_signal_time < self.signal_cooldown * 60:
            return None

        signals = []

        # 1. Lorentzian分类信号
        if self.state_map.lorentzian_confidence > 0.6:
            if self.state_map.lorentzian_signal == 'buy':
                signals.append({
                    'type': SignalType.LORENTZIAN_BUY,
                    'strength': self.state_map.lorentzian_confidence,
                    'direction': 'buy'
                })
            elif self.state_map.lorentzian_signal == 'sell':
                signals.append({
                    'type': SignalType.LORENTZIAN_SELL,
                    'strength': self.state_map.lorentzian_confidence,
                    'direction': 'sell'
                })

        # 2. 神经网络预测信号
        if self.state_map.nn_confidence > 0.7:
            if self.state_map.nn_prediction > 0.5:
                signals.append({
                    'type': SignalType.NEURAL_PREDICTION,
                    'strength': self.state_map.nn_confidence,
                    'direction': 'buy'
                })
            elif self.state_map.nn_prediction < -0.5:
                signals.append({
                    'type': SignalType.NEURAL_PREDICTION,
                    'strength': self.state_map.nn_confidence,
                    'direction': 'sell'
                })

        # 3. 自适应趋势信号
        if self.current_market_state and self.current_market_state.confidence > 0.7:
            if self.current_market_state.regime == MarketRegime.BULL_TREND:
                if self.state_map.rsi < self.state_map.adaptive_rsi_upper:
                    signals.append({
                        'type': SignalType.ADAPTIVE_TREND,
                        'strength': self.current_market_state.confidence,
                        'direction': 'buy'
                    })
            elif self.current_market_state.regime == MarketRegime.BEAR_TREND:
                if self.state_map.rsi > self.state_map.adaptive_rsi_lower:
                    signals.append({
                        'type': SignalType.ADAPTIVE_TREND,
                        'strength': self.current_market_state.confidence,
                        'direction': 'sell'
                    })

        # 4. 状态切换信号
        if (self.current_market_state and
            self.current_market_state.transition_probability > 0.8):
            signals.append({
                'type': SignalType.REGIME_SWITCH,
                'strength': self.current_market_state.transition_probability,
                'direction': 'hold'  # 状态切换时保持观望
            })

        # 信号融合 - 使用加权投票
        return self._fuse_next_gen_signals(signals, kline)

    def _fuse_next_gen_signals(self, signals: List[Dict], kline: KLineData) -> Optional[Dict]:
        """下一代信号融合算法"""
        if not signals:
            return None

        # 按信号类型分组
        buy_signals = [s for s in signals if s['direction'] == 'buy']
        sell_signals = [s for s in signals if s['direction'] == 'sell']
        hold_signals = [s for s in signals if s['direction'] == 'hold']

        # 如果有强烈的hold信号，优先考虑
        if hold_signals and any(s['strength'] > 0.8 for s in hold_signals):
            return None

        # 计算加权得分
        buy_score = sum(s['strength'] * self._get_signal_weight(s['type']) for s in buy_signals)
        sell_score = sum(s['strength'] * self._get_signal_weight(s['type']) for s in sell_signals)

        # 最小信号强度阈值
        min_threshold = 1.5

        if buy_score > sell_score and buy_score > min_threshold:
            direction = 'buy'
            confidence = min(buy_score / 3.0, 1.0)  # 归一化到[0,1]
        elif sell_score > buy_score and sell_score > min_threshold:
            direction = 'sell'
            confidence = min(sell_score / 3.0, 1.0)
        else:
            return None

        # 计算止损止盈
        stop_loss, take_profit = self._calculate_adaptive_stops(kline, direction)

        return {
            'direction': direction,
            'confidence': confidence,
            'price': kline.close,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'timestamp': kline.timestamp,
            'signals_used': len(signals),
            'buy_score': buy_score,
            'sell_score': sell_score
        }

    def _get_signal_weight(self, signal_type: SignalType) -> float:
        """获取信号权重"""
        weights = {
            SignalType.LORENTZIAN_BUY: 1.2,      # Lorentzian权重较高
            SignalType.LORENTZIAN_SELL: 1.2,
            SignalType.NEURAL_PREDICTION: 1.0,   # 神经网络标准权重
            SignalType.ADAPTIVE_TREND: 0.8,      # 趋势信号权重较低
            SignalType.REGIME_SWITCH: 1.5        # 状态切换权重最高
        }
        return weights.get(signal_type, 1.0)

    def _calculate_adaptive_stops(self, kline: KLineData, direction: str) -> Tuple[float, float]:
        """计算自适应止损止盈"""
        atr_multiplier = self.state_map.adaptive_atr_mult
        atr_value = self.state_map.atr

        # 根据市场状态调整
        if self.current_market_state:
            if self.current_market_state.volatility_regime == "high":
                atr_multiplier *= 1.3  # 高波动时增加止损距离
            elif self.current_market_state.volatility_regime == "low":
                atr_multiplier *= 0.8  # 低波动时减少止损距离

        if direction == 'buy':
            stop_loss = kline.close - (atr_value * atr_multiplier)
            take_profit = kline.close + (atr_value * atr_multiplier * 2.5)  # 更高的盈亏比
        else:
            stop_loss = kline.close + (atr_value * atr_multiplier)
            take_profit = kline.close - (atr_value * atr_multiplier * 2.5)

        return stop_loss, take_profit

    def _execute_next_gen_decision(self, signal: Dict):
        """执行下一代交易决策"""
        if not self.trading:
            return

        # 高级风险检查
        if not self._advanced_risk_check(signal):
            return

        # 获取当前仓位
        position = self.get_position(self.params_map.instrument_id)

        # 计算动态仓位大小
        volume = self._calculate_dynamic_position_size(signal)

        # 执行交易
        try:
            if signal['direction'] == 'buy':
                if position.net_position <= 0:
                    order_id = self.send_order(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        volume=volume,
                        price=signal['price'],
                        order_direction='buy'
                    )

                    if order_id:
                        self.last_signal_time = signal['timestamp']
                        self.logger.info(f"执行买入信号: 价格={signal['price']:.2f}, "
                                       f"置信度={signal['confidence']:.2%}, "
                                       f"信号数={signal['signals_used']}")

            elif signal['direction'] == 'sell':
                if position.net_position >= 0:
                    order_id = self.send_order(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        volume=volume,
                        price=signal['price'],
                        order_direction='sell'
                    )

                    if order_id:
                        self.last_signal_time = signal['timestamp']
                        self.logger.info(f"执行卖出信号: 价格={signal['price']:.2f}, "
                                       f"置信度={signal['confidence']:.2%}, "
                                       f"信号数={signal['signals_used']}")

        except Exception as e:
            self.logger.error(f"交易执行失败: {e}")

    def _advanced_risk_check(self, signal: Dict) -> bool:
        """高级风险检查"""
        # 基础风险检查
        if signal['confidence'] < 0.6:
            return False

        # 市场状态风险检查
        if (self.current_market_state and
            self.current_market_state.transition_probability > 0.9):
            self.logger.warning("市场状态高度不确定，跳过交易")
            return False

        # 波动率风险检查
        if self.state_map.atr > np.mean([p['close'] for p in self.price_history[-20:]]) * 0.05:
            self.logger.warning("市场波动率过高，跳过交易")
            return False

        return True

    def _calculate_dynamic_position_size(self, signal: Dict) -> int:
        """计算动态仓位大小"""
        base_risk = self.adaptive_params.get('risk_per_trade', 0.02)

        # 根据信号置信度调整
        confidence_multiplier = signal['confidence']

        # 根据市场状态调整
        regime_multiplier = 1.0
        if self.current_market_state:
            if self.current_market_state.regime in [MarketRegime.BULL_TREND, MarketRegime.BEAR_TREND]:
                regime_multiplier = 1.2  # 趋势市场增加仓位
            else:
                regime_multiplier = 0.8  # 震荡市场减少仓位

        # 计算最终仓位
        adjusted_risk = base_risk * confidence_multiplier * regime_multiplier

        # 基于止损距离计算仓位
        stop_distance = abs(signal['price'] - signal['stop_loss']) / signal['price']
        if stop_distance > 0:
            position_size = int(adjusted_risk / stop_distance)
            return min(position_size, self.params_map.max_position_size)

        return 1

    def _update_performance_metrics(self, trade: TradeData):
        """更新性能指标"""
        # 计算交易盈亏
        pnl = getattr(trade, 'pnl', 0)
        self.performance_history.append(pnl)

        # 记录详细信息
        trade_info = {
            'timestamp': trade.timestamp,
            'direction': trade.order_direction,
            'price': trade.price,
            'volume': trade.volume,
            'pnl': pnl,
            'regime': self.state_map.current_regime,
            'lorentzian_confidence': self.state_map.lorentzian_confidence,
            'nn_confidence': self.state_map.nn_confidence
        }

        self.logger.info(f"交易完成: {trade_info}")

    def _update_ui(self, kline: KLineData):
        """更新UI显示"""
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": getattr(self, 'signal_price', 0),
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        if self.trading:
            self.update_status_bar()

    def _generate_performance_report(self):
        """生成性能报告"""
        if not self.performance_history:
            return

        total_trades = len(self.performance_history)
        winning_trades = sum(1 for pnl in self.performance_history if pnl > 0)
        losing_trades = sum(1 for pnl in self.performance_history if pnl < 0)

        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        total_pnl = sum(self.performance_history)

        avg_win = np.mean([pnl for pnl in self.performance_history if pnl > 0]) if winning_trades > 0 else 0
        avg_loss = np.mean([pnl for pnl in self.performance_history if pnl < 0]) if losing_trades > 0 else 0
        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')

        report = f"""
        ========== 下一代策略性能报告 ==========
        总交易次数: {total_trades}
        盈利交易: {winning_trades}
        亏损交易: {losing_trades}
        胜率: {win_rate:.2%}
        总盈亏: {total_pnl:.2f}
        平均盈利: {avg_win:.2f}
        平均亏损: {avg_loss:.2f}
        盈亏比: {profit_factor:.2f}

        === 技术指标统计 ===
        Lorentzian平均置信度: {np.mean([h for h in [self.state_map.lorentzian_confidence] if h > 0]):.2%}
        神经网络平均置信度: {np.mean([h for h in [self.state_map.nn_confidence] if h > 0]):.2%}
        市场状态分布: {self.state_map.current_regime}

        === 自适应参数 ===
        当前ATR倍数: {self.state_map.adaptive_atr_mult:.2f}
        当前RSI上限: {self.state_map.adaptive_rsi_upper:.1f}
        当前RSI下限: {self.state_map.adaptive_rsi_lower:.1f}
        =====================================
        """

        self.logger.info(report)

        # 保存报告
        try:
            with open(f"nextgen_strategy_report_{self.params_map.instrument_id}.txt", "w", encoding="utf-8") as f:
                f.write(report)
        except Exception as e:
            self.logger.error(f"保存报告失败: {e}")

    # 公共接口方法
    def get_strategy_status(self) -> Dict:
        """获取策略状态"""
        return {
            'current_regime': self.state_map.current_regime,
            'regime_confidence': self.state_map.regime_confidence,
            'lorentzian_signal': self.state_map.lorentzian_signal,
            'lorentzian_confidence': self.state_map.lorentzian_confidence,
            'nn_prediction': self.state_map.nn_prediction,
            'nn_confidence': self.state_map.nn_confidence,
            'adaptive_atr_mult': self.state_map.adaptive_atr_mult,
            'adaptive_rsi_upper': self.state_map.adaptive_rsi_upper,
            'adaptive_rsi_lower': self.state_map.adaptive_rsi_lower,
            'total_trades': len(self.performance_history),
            'recent_performance': np.mean(list(self.performance_history)[-10:]) if len(self.performance_history) >= 10 else 0
        }

    def force_regime_detection(self):
        """强制重新检测市场状态"""
        if len(self.price_history) >= 20:
            self._update_market_regime()
            self.logger.info(f"强制状态检测完成: {self.state_map.current_regime}")

    def reset_adaptive_parameters(self):
        """重置自适应参数"""
        self.adaptive_params = {
            'atr_multiplier': 2.0,
            'rsi_upper': 70.0,
            'rsi_lower': 30.0,
            'risk_per_trade': 0.02
        }
        self.logger.info("自适应参数已重置")

    def export_trading_data(self, filename: str):
        """导出交易数据"""
        try:
            data = {
                'performance_history': list(self.performance_history),
                'adaptive_params': self.adaptive_params,
                'current_state': {
                    'regime': self.state_map.current_regime,
                    'regime_confidence': self.state_map.regime_confidence,
                    'lorentzian_signal': self.state_map.lorentzian_signal,
                    'lorentzian_confidence': self.state_map.lorentzian_confidence,
                    'nn_prediction': self.state_map.nn_prediction,
                    'nn_confidence': self.state_map.nn_confidence
                }
            }

            import json
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"交易数据已导出到: {filename}")
        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")


# 使用示例
if __name__ == "__main__":
    # 创建下一代策略实例
    strategy = NextGenTradingStrategy()

    # 设置参数
    strategy.params_map.exchange = "SHFE"
    strategy.params_map.instrument_id = "rb2501"
    strategy.params_map.kline_style = "M1"

    # Lorentzian参数
    strategy.params_map.lorentzian_neighbors = 8
    strategy.params_map.lorentzian_lookback = 100

    # HMM参数
    strategy.params_map.hmm_states = 5
    strategy.params_map.hmm_lookback = 200

    # 自适应参数
    strategy.params_map.adaptation_rate = 0.1
    strategy.params_map.adaptation_period = 50

    # 神经网络参数
    strategy.params_map.nn_hidden_size = 16
    strategy.params_map.nn_lookback = 20

    print("下一代交易策略已创建")
    print("集成技术:")
    print("1. 🧠 Lorentzian分类器 - 基于距离的机器学习")
    print("2. 🔄 Hidden Markov模型 - 市场状态识别")
    print("3. ⚡ 自适应参数系统 - 动态优化")
    print("4. 🤖 轻量级神经网络 - 价格预测")
    print("5. 📊 多维特征融合 - 智能信号生成")
    print("6. 🛡️ 高级风险管理 - 动态止损止盈")

    print(f"\n相比传统多指标加权策略的优势:")
    print("✅ 从静态权重 → 动态自适应权重")
    print("✅ 从简单融合 → 机器学习分类")
    print("✅ 从固定参数 → 实时参数优化")
    print("✅ 从单一模型 → 多模型集成")
    print("✅ 从经验判断 → 数据驱动决策")
