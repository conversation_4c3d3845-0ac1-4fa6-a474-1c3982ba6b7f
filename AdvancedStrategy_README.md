# 高级全能交易策略 (Advanced Trading Strategy)

基于DemoKC.py改进的高性能程序化交易策略，具备自主趋势判断、多空方向选择和先进风险管理功能。

## 🚀 主要特性

### 1. 智能市场状态识别
- **趋势识别**: 自动识别单边上涨、单边下跌、震荡行情
- **多时间框架分析**: 支持M1、M5、M15等多个时间周期
- **动态阈值调整**: 根据市场波动自动调整判断阈值

### 2. 多指标信号融合
- **趋势跟踪**: EMA交叉、移动平均线系统
- **动量指标**: RSI超买超卖信号
- **反转信号**: MACD金叉死叉
- **突破信号**: 布林带突破策略
- **信号权重**: 智能信号强度评估和置信度计算

### 3. 先进风险管理
- **动态止损**: 基于ATR的自适应止损
- **追踪止损**: 盈利保护机制
- **仓位管理**: 基于风险的仓位计算
- **回撤控制**: 最大回撤限制
- **日损失限制**: 防止单日过度亏损

### 4. 机器学习增强
- **特征提取**: 自动提取价格和技术指标特征
- **预测模型**: 简化的机器学习预测框架
- **自适应学习**: 基于历史数据的策略优化

### 5. 性能监控与分析
- **实时统计**: 胜率、盈亏比、夏普比率等
- **详细报告**: 完整的交易记录和性能分析
- **可视化**: 资金曲线、回撤分析图表

## 📁 文件结构

```
├── AdvancedTradingStrategy.py  # 主策略文件
├── StrategyTester.py          # 回测和优化工具
├── DemoKC.py                  # 原始策略（参考）
└── AdvancedStrategy_README.md # 使用说明
```

## 🛠️ 安装要求

```python
# 必需依赖
numpy>=1.21.0
pandas>=1.3.0
matplotlib>=3.5.0

# 无限易Pro交易框架
pythongo  # 需要从无限易Pro获取
```

## 🎯 快速开始

### 1. 基本使用

```python
from AdvancedTradingStrategy import AdvancedTradingStrategy

# 创建策略实例
strategy = AdvancedTradingStrategy()

# 设置交易参数
strategy.params_map.exchange = "SHFE"
strategy.params_map.instrument_id = "rb2501"
strategy.params_map.kline_style = "M1"
strategy.params_map.max_position_size = 10
strategy.params_map.risk_per_trade = 0.02

# 启动策略
strategy.on_start()
```

### 2. 回测验证

```python
from StrategyTester import StrategyTester

# 创建测试器
tester = StrategyTester(strategy)

# 加载测试数据
tester.load_test_data(generate_synthetic=True)

# 运行回测
result = tester.run_backtest(start_capital=100000)

# 查看结果
print(f"总收益率: {result.total_return:.2%}")
print(f"夏普比率: {result.sharpe_ratio:.2f}")
print(f"最大回撤: {result.max_drawdown:.2%}")
```

### 3. 参数优化

```python
# 定义优化范围
param_ranges = {
    'trend_threshold': [0.01, 0.02, 0.03],
    'risk_per_trade': [0.01, 0.02, 0.03],
    'atr_multiplier': [1.5, 2.0, 2.5]
}

# 运行优化
optimization_result = tester.optimize_parameters(param_ranges)
print(f"最优参数: {optimization_result['best_params']}")
```

## ⚙️ 核心参数说明

### 趋势识别参数
- `trend_period`: 趋势识别周期 (默认: 20)
- `trend_threshold`: 趋势判断阈值 (默认: 0.02)

### 风险管理参数
- `max_position_size`: 最大仓位 (默认: 10)
- `risk_per_trade`: 单笔风险比例 (默认: 0.02)
- `max_daily_loss`: 日最大亏损 (默认: 0.05)
- `atr_multiplier`: ATR止损倍数 (默认: 2.0)
- `trailing_stop_pct`: 追踪止损百分比 (默认: 0.01)

### 机器学习参数
- `ml_lookback`: ML回看周期 (默认: 100)
- `ml_features`: 特征数量 (默认: 20)

## 📊 性能指标

策略提供以下关键性能指标：

- **收益指标**: 总收益率、年化收益率
- **风险指标**: 最大回撤、波动率、夏普比率、卡尔玛比率
- **交易指标**: 胜率、盈亏比、总交易次数、平均持仓时间

## 🔄 与原DemoKC策略对比

| 特性 | DemoKC | AdvancedTradingStrategy |
|------|--------|------------------------|
| 指标使用 | 单一指标 | 多指标融合 |
| 市场识别 | 无 | 智能趋势识别 |
| 风险管理 | 基础 | 先进风险控制 |
| 信号质量 | 简单 | 智能信号评估 |
| 机器学习 | 无 | 集成ML预测 |
| 性能监控 | 基础 | 全面分析报告 |

## 🎨 可视化功能

```python
# 绘制回测结果
tester.plot_results(result, save_path="backtest_chart.png")

# 生成详细报告
report = tester.generate_report(result, "strategy_report.txt")
```

## 🔧 高级功能

### 1. 策略状态监控
```python
status = strategy.get_strategy_status()
print(f"当前市场状态: {status['market_regime']}")
print(f"信号方向: {status['signal_direction']}")
```

### 2. 信号导出
```python
strategy.export_signals("signals_history.json")
```

### 3. 多策略对比
```python
from StrategyTester import StrategyComparison

comparison = StrategyComparison()
comparison.add_strategy("Advanced", advanced_strategy)
comparison.add_strategy("Demo", demo_strategy)
results = comparison.run_comparison(test_data)
comparison.plot_comparison()
```

## 📈 实盘部署建议

1. **参数调优**: 使用历史数据进行充分的参数优化
2. **风险控制**: 设置合理的仓位和止损参数
3. **监控机制**: 实时监控策略表现和风险指标
4. **定期评估**: 定期评估策略表现并调整参数

## ⚠️ 风险提示

- 本策略仅供学习和研究使用
- 实盘交易前请充分测试和验证
- 过往表现不代表未来收益
- 请根据自身风险承受能力调整参数

## 🤝 贡献指南

欢迎提交改进建议和bug报告。主要改进方向：

1. 更多技术指标集成
2. 高级机器学习算法
3. 更精细的风险管理
4. 性能优化

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

---

**注意**: 本策略基于无限易Pro交易框架开发，需要相应的交易环境支持。
