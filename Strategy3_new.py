# -*- coding: utf-8 -*-
"""
Strategy3.py - 高级模糊推理交易策略 (无限易Pro兼容版本)
基于无限易Pro交易软件架构的综合交易策略

主要特性：
1. 完全兼容无限易Pro交易软件架构
2. HULL+STC技术指标主信号
3. 模糊推理辅助判断系统
4. 参数可控：通过Params类设置各模块开启状态
5. 状态管理：通过State类管理策略状态
6. K线驱动：基于K线数据进行策略计算

架构说明：
- 继承BaseStrategy基类
- 使用Params和State类管理参数和状态
- 实现on_tick, on_bar, on_start, on_stop等标准接口
- 支持主图和副图指标显示
"""

from typing import Dict, List, Optional, Tuple, Any, Union
import time
import math
import warnings
from collections import deque
import numpy as np

warnings.filterwarnings('ignore')

# 导入无限易Pro基础模块
try:
    from pythongo.base import BaseParams, BaseState, Field
    from pythongo.classdef import KLineData, OrderData, TickData, TradeData, BarData
    from pythongo.ui import BaseStrategy
    from pythongo.utils import KLineGenerator
    PYTHONGO_AVAILABLE = True
except ImportError:
    # 模拟无限易Pro模块（用于测试）
    PYTHONGO_AVAILABLE = False

    class Field:
        def __init__(self, default=None, title="", **kwargs):
            self.default = default
            self.title = title

    class BaseParams:
        pass

    class BaseState:
        pass

    class BaseStrategy:
        def __init__(self):
            self.trading = False
            self.widget = None

        def on_tick(self, tick):
            pass

        def on_bar(self, bar):
            pass

        def on_start(self):
            pass

        def on_stop(self):
            pass

        def on_order_cancel(self, order):
            pass

        def on_trade(self, trade, log=False):
            pass

        def get_position(self, instrument_id):
            class Position:
                def __init__(self):
                    self.net_position = 0
            return Position()

        def send_order(self, **kwargs):
            return "test_order_id"

        def cancel_order(self, order_id):
            pass

        def auto_close_position(self, **kwargs):
            return "test_close_order_id"

        def update_status_bar(self):
            pass

        def write_log(self, msg, level="INFO"):
            print(f"[{level}] {msg}")

    class KLineData:
        def __init__(self, open=100, high=101, low=99, close=100.5, volume=1000):
            self.open = open
            self.high = high
            self.low = low
            self.close = close
            self.volume = volume

    class BarData:
        def __init__(self, symbol="", datetime=None, open=100, high=101, low=99, close=100.5, volume=1000):
            self.symbol = symbol
            self.datetime = datetime
            self.open = open
            self.high = high
            self.low = low
            self.close = close
            self.volume = volume

    class TickData:
        def __init__(self, last_price=100, volume=1000):
            self.last_price = last_price
            self.volume = volume
            self.ask_price1 = last_price + 0.01
            self.bid_price1 = last_price - 0.01
            self.ask_price2 = last_price + 0.02
            self.bid_price2 = last_price - 0.02

    class OrderData:
        pass

    class TradeData:
        def __init__(self, direction="buy", price=100, volume=1):
            self.direction = direction
            self.price = price
            self.volume = volume

    class KLineGenerator:
        def __init__(self, **kwargs):
            self.callback = kwargs.get('callback')
            self.real_time_callback = kwargs.get('real_time_callback')

        def update_tick(self, tick):
            pass

        def generate(self):
            pass

# ==================== 策略参数配置 ====================

class Params(BaseParams):
    """参数映射模型 - 兼容无限易Pro架构"""
    # 基础交易参数
    symbol: str = Field(default="", title="合约代码")
    exchange: str = Field(default="", title="交易所代码")
    interval: str = Field(default="1m", title="K线周期")

    # 主技术指标参数
    hull_period: int = Field(default=9, title="Hull MA周期")
    stc_fast_period: int = Field(default=23, title="STC快线周期")
    stc_slow_period: int = Field(default=50, title="STC慢线周期")
    stc_cycle_period: int = Field(default=10, title="STC循环周期")

    # 交易执行参数
    fixed_size: int = Field(default=1, title="固定交易手数")
    price_tick: float = Field(default=0.01, title="最小价格变动单位")
    slippage: float = Field(default=0.01, title="滑点")

    # 模块开关参数
    enable_fuzzy: bool = Field(default=True, title="启用模糊推理")
    enable_advanced: bool = Field(default=True, title="启用高级分析")

    # 风险管理参数
    stop_loss_pct: float = Field(default=0.02, title="止损百分比")
    take_profit_pct: float = Field(default=0.04, title="止盈百分比")
    max_position: int = Field(default=5, title="最大持仓手数")

    # 信号阈值参数
    signal_threshold: float = Field(default=0.3, title="信号阈值")
    fuzzy_threshold: float = Field(default=0.5, title="模糊信号阈值")

    # 高级分析参数
    analysis_window: int = Field(default=20, title="分析窗口")
    confidence_level: float = Field(default=0.8, title="置信度水平")

class State(BaseState):
    """状态映射模型 - 兼容无限易Pro架构"""
    # 主图技术指标状态
    hull_ma: float = Field(default=0.0, title="Hull移动平均线")
    stc_value: float = Field(default=50.0, title="STC指标值")
    stc_signal: float = Field(default=50.0, title="STC信号线")

    # 副图指标状态
    fuzzy_signal: float = Field(default=0.0, title="模糊推理信号")
    final_signal: float = Field(default=0.0, title="最终综合信号")
    signal_strength: float = Field(default=0.0, title="信号强度")

    # 市场状态指标
    volatility: float = Field(default=0.0, title="市场波动率")
    trend_strength: float = Field(default=0.0, title="趋势强度")
    market_regime: str = Field(default="normal", title="市场状态")

    # 交易状态
    position: int = Field(default=0, title="当前持仓")
    entry_price: float = Field(default=0.0, title="入场价格")
    unrealized_pnl: float = Field(default=0.0, title="未实现盈亏")
    last_signal_price: float = Field(default=0.0, title="最后信号价格")

    # 性能统计
    total_trades: int = Field(default=0, title="总交易次数")
    winning_trades: int = Field(default=0, title="盈利交易次数")
    total_profit: float = Field(default=0.0, title="总盈利")
    win_rate: float = Field(default=0.0, title="胜率")

# ==================== 技术指标实现 ====================

class HullMovingAverage:
    """Hull移动平均线"""
    def __init__(self, period: int = 9):
        self.period = period
        self.values = deque(maxlen=period * 2)
        self.wma_half = deque(maxlen=period // 2)
        self.wma_full = deque(maxlen=period)

    def update(self, price: float) -> Optional[float]:
        """更新Hull MA值"""
        self.values.append(price)

        if len(self.values) < self.period:
            return None

        # 计算WMA(period/2) * 2
        half_period = self.period // 2
        wma_half = self._calculate_wma(list(self.values)[-half_period:], half_period)

        # 计算WMA(period)
        wma_full = self._calculate_wma(list(self.values), self.period)

        # Hull MA = WMA(2*WMA(period/2) - WMA(period), sqrt(period))
        hull_raw = 2 * wma_half - wma_full
        self.wma_half.append(hull_raw)

        sqrt_period = int(math.sqrt(self.period))
        if len(self.wma_half) >= sqrt_period:
            hull_ma = self._calculate_wma(list(self.wma_half)[-sqrt_period:], sqrt_period)
            return hull_ma

        return None

    def _calculate_wma(self, values: List[float], period: int) -> float:
        """计算加权移动平均"""
        if len(values) < period:
            return sum(values) / len(values) if values else 0.0

        weights = list(range(1, period + 1))
        weighted_sum = sum(v * w for v, w in zip(values[-period:], weights))
        weight_sum = sum(weights)
        return weighted_sum / weight_sum

class SchaffTrendCycle:
    """Schaff趋势周期指标"""
    def __init__(self, fast_period: int = 23, slow_period: int = 50, cycle_period: int = 10):
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.cycle_period = cycle_period
        self.prices = deque(maxlen=max(fast_period, slow_period) + cycle_period)
        self.macd_values = deque(maxlen=cycle_period * 2)
        self.stoch_values = deque(maxlen=cycle_period * 2)
        self.fast_ema_cache = None
        self.slow_ema_cache = None

    def update(self, high: float, low: float, close: float) -> Tuple[Optional[float], Optional[float]]:
        """更新STC值"""
        self.prices.append({'high': high, 'low': low, 'close': close})

        if len(self.prices) < self.slow_period:
            return None, None

        # 计算MACD
        macd = self._calculate_macd(close)
        if macd is None:
            return None, None

        self.macd_values.append(macd)

        # 计算第一次随机指标
        if len(self.macd_values) >= self.cycle_period:
            stoch1 = self._calculate_stochastic(list(self.macd_values), self.cycle_period)
            self.stoch_values.append(stoch1)

            # 计算第二次随机指标（STC值）
            if len(self.stoch_values) >= self.cycle_period:
                stc_value = self._calculate_stochastic(list(self.stoch_values), self.cycle_period)

                # 计算信号线（STC的移动平均）
                signal_period = 3
                if len(self.stoch_values) >= signal_period:
                    signal_line = sum(list(self.stoch_values)[-signal_period:]) / signal_period
                    return stc_value, signal_line

        return None, None

    def _calculate_macd(self, close: float) -> Optional[float]:
        """计算MACD"""
        # 使用缓存优化EMA计算
        fast_ema = self._calculate_ema(close, self.fast_period, self.fast_ema_cache)
        slow_ema = self._calculate_ema(close, self.slow_period, self.slow_ema_cache)

        # 更新缓存
        self.fast_ema_cache = fast_ema
        self.slow_ema_cache = slow_ema

        if fast_ema is None or slow_ema is None:
            return None

        return fast_ema - slow_ema

    def _calculate_ema(self, current_value: float, period: int, previous_ema: Optional[float]) -> Optional[float]:
        """计算指数移动平均"""
        if len(self.prices) < period:
            closes = [p['close'] for p in self.prices]
            if len(closes) == 0:
                return None
            return sum(closes) / len(closes)

        multiplier = 2.0 / (period + 1)

        if previous_ema is None:
            # 初始化EMA
            closes = [p['close'] for p in list(self.prices)[-period:]]
            ema = closes[0]
            for value in closes[1:]:
                ema = (value * multiplier) + (ema * (1 - multiplier))
            return ema
        else:
            # 使用前一个EMA值更新
            return (current_value * multiplier) + (previous_ema * (1 - multiplier))

    def _calculate_stochastic(self, values: List[float], period: int) -> float:
        """计算随机指标"""
        if len(values) < period:
            return 50.0

        recent_values = values[-period:]
        highest = max(recent_values)
        lowest = min(recent_values)
        current = values[-1]

        if highest == lowest:
            return 50.0

        stoch = ((current - lowest) / (highest - lowest)) * 100
        return max(0, min(100, stoch))

# ==================== 模糊推理系统 ====================

class FuzzyInferenceSystem:
    """模糊推理系统"""
    def __init__(self):
        self.rules = []
        self.membership_functions = {}

    def add_rule(self, antecedents: List[str], consequent: str, weight: float = 1.0):
        """添加模糊规则"""
        self.rules.append({
            'antecedents': antecedents,
            'consequent': consequent,
            'weight': weight
        })

    def define_membership_function(self, name: str, function_type: str, params: Dict[str, float]):
        """定义隶属度函数"""
        self.membership_functions[name] = {
            'type': function_type,
            'params': params
        }

    def calculate_membership(self, value: float, function_name: str) -> float:
        """计算隶属度"""
        if function_name not in self.membership_functions:
            return 0.0

        func = self.membership_functions[function_name]
        func_type = func['type']
        params = func['params']

        if func_type == 'triangle':
            a, b, c = params['a'], params['b'], params['c']
            if value <= a or value >= c:
                return 0.0
            elif a < value <= b:
                return (value - a) / (b - a)
            else:  # b < value < c
                return (c - value) / (c - b)

        elif func_type == 'trapezoid':
            a, b, c, d = params['a'], params['b'], params['c'], params['d']
            if value <= a or value >= d:
                return 0.0
            elif a < value < b:
                return (value - a) / (b - a)
            elif b <= value <= c:
                return 1.0
            else:  # c < value < d
                return (d - value) / (d - c)

        elif func_type == 'gaussian':
            center, width = params['center'], params['width']
            return math.exp(-0.5 * ((value - center) / width) ** 2)

        return 0.0

    def infer(self, inputs: Dict[str, float]) -> Dict[str, float]:
        """模糊推理"""
        consequents = {}

        for rule in self.rules:
            # 计算规则前提的隶属度
            antecedent_values = []
            for ant in rule['antecedents']:
                if ':' in ant:
                    var_name, func_name = ant.split(':')
                    if var_name in inputs:
                        value = inputs[var_name]
                        membership = self.calculate_membership(value, func_name)
                        antecedent_values.append(membership)

            # 使用最小值作为规则强度
            if antecedent_values:
                rule_strength = min(antecedent_values) * rule['weight']

                # 更新结论的强度
                consequent = rule['consequent']
                if consequent in consequents:
                    consequents[consequent] = max(consequents[consequent], rule_strength)
                else:
                    consequents[consequent] = rule_strength

        return consequents

    def defuzzify(self, outputs: Dict[str, float], method: str = 'centroid') -> float:
        """去模糊化"""
        if not outputs:
            return 0.0

        if method == 'centroid':
            # 简化的重心法
            numerator = 0.0
            denominator = 0.0

            for output, strength in outputs.items():
                # 将输出名称映射到数值
                if output == 'buy':
                    value = 1.0
                elif output == 'sell':
                    value = -1.0
                elif output == 'hold':
                    value = 0.0
                else:
                    value = 0.0

                numerator += value * strength
                denominator += strength

            if denominator == 0:
                return 0.0
            return numerator / denominator

        elif method == 'max_membership':
            # 最大隶属度法
            max_output = max(outputs.items(), key=lambda x: x[1])[0]
            if max_output == 'buy':
                return 1.0
            elif max_output == 'sell':
                return -1.0
            else:
                return 0.0

        return 0.0

# ==================== 策略主体 ====================

class Strategy3(BaseStrategy):
    """高级模糊推理交易策略"""

    def __init__(self):
        """初始化策略"""
        super().__init__()

        # 初始化参数和状态
        self.params = Params()
        self.state = State()

        # 初始化技术指标
        self.hull_ma = None
        self.stc = None
        self.fuzzy_system = None

        # 初始化K线生成器
        self.kline_generator = KLineGenerator(
            callback=self.on_kline,
            real_time_callback=self.on_kline_realtime
        )

        # 初始化数据缓存
        self.price_history = deque(maxlen=100)
        self.volatility_history = deque(maxlen=20)

        # 初始化交易状态
        self.last_signal = 0
        self.entry_time = 0
        self.stop_loss_price = 0
        self.take_profit_price = 0

    def on_start(self):
        """策略启动回调"""
        self.write_log("策略启动")

        # 初始化技术指标
        self.hull_ma = HullMovingAverage(period=self.params.hull_period)
        self.stc = SchaffTrendCycle(
            fast_period=self.params.stc_fast_period,
            slow_period=self.params.stc_slow_period,
            cycle_period=self.params.stc_cycle_period
        )

        # 初始化模糊推理系统
        self._init_fuzzy_system()

        # 重置状态
        self._reset_state()

        # 更新状态栏
        self.update_status_bar()

    def on_stop(self):
        """策略停止回调"""
        self.write_log("策略停止")

        # 平仓
        if self.state.position != 0:
            self._close_position("策略停止")

    def on_tick(self, tick: TickData):
        """Tick数据回调"""
        # 更新K线生成器
        self.kline_generator.update_tick(tick)

    def on_bar(self, bar: BarData):
        """K线数据回调"""
        # 更新价格历史
        self.price_history.append(bar.close)

        # 计算波动率
        if len(self.price_history) >= 2:
            volatility = abs(bar.close - self.price_history[-2]) / self.price_history[-2]
            self.volatility_history.append(volatility)
            self.state.volatility = float(np.mean(list(self.volatility_history)))

        # 更新技术指标
        self._update_indicators(bar)

        # 生成交易信号
        signal = self._generate_signal(bar)

        # 执行交易
        self._execute_trade(signal, bar)

        # 更新状态
        self._update_state(bar)

        # 更新状态栏
        self.update_status_bar()

    def on_kline(self, kline: KLineData):
        """K线数据回调"""
        # 转换为BarData并调用on_bar
        bar = BarData(
            symbol=kline.symbol,
            datetime=kline.datetime,
            open=kline.open,
            high=kline.high,
            low=kline.low,
            close=kline.close,
            volume=kline.volume
        )
        self.on_bar(bar)

    def on_kline_realtime(self, kline: KLineData):
        """实时K线数据回调"""
        # 处理实时K线数据
        pass

    def on_order_cancel(self, order: OrderData):
        """订单撤销回调"""
        self.write_log(f"订单已撤销: {order}")

    def on_trade(self, trade: TradeData, log=False):
        """成交回调"""
        if log:
            self.write_log(f"成交: {trade.direction} {trade.volume}手 @ {trade.price}")

        # 更新持仓状态
        if trade.direction == "buy":
            self.state.position += trade.volume
            if self.state.position == trade.volume:  # 新开仓
                self.state.entry_price = trade.price
                self.entry_time = time.time()

                # 设置止损和止盈价格
                self.stop_loss_price = trade.price * (1 - self.params.stop_loss_pct)
                self.take_profit_price = trade.price * (1 + self.params.take_profit_pct)
        else:  # sell
            self.state.position -= trade.volume
            if self.state.position == 0:  # 平仓
                # 计算盈亏
                pnl = (trade.price - self.state.entry_price) * trade.volume
                self.state.unrealized_pnl = 0
                self.state.total_profit += pnl

                # 更新交易统计
                self.state.total_trades += 1
                if pnl > 0:
                    self.state.winning_trades += 1

                # 计算胜率
                if self.state.total_trades > 0:
                    self.state.win_rate = self.state.winning_trades / self.state.total_trades

                self.write_log(f"平仓盈亏: {pnl:.2f}")

    def _init_fuzzy_system(self):
        """初始化模糊推理系统"""
        self.fuzzy_system = FuzzyInferenceSystem()

        # 定义隶属度函数
        # 趋势强度
        self.fuzzy_system.define_membership_function(
            "trend_weak", "triangle", {"a": -1, "b": -0.5, "c": 0}
        )
        self.fuzzy_system.define_membership_function(
            "trend_neutral", "triangle", {"a": -0.5, "b": 0, "c": 0.5}
        )
        self.fuzzy_system.define_membership_function(
            "trend_strong", "triangle", {"a": 0, "b": 0.5, "c": 1}
        )

        # 波动率
        self.fuzzy_system.define_membership_function(
            "volatility_low", "triangle", {"a": 0, "b": 0.01, "c": 0.02}
        )
        self.fuzzy_system.define_membership_function(
            "volatility_medium", "triangle", {"a": 0.01, "b": 0.02, "c": 0.03}
        )
        self.fuzzy_system.define_membership_function(
            "volatility_high", "triangle", {"a": 0.02, "b": 0.03, "c": 0.05}
        )

        # 添加模糊规则
        self.fuzzy_system.add_rule(
            ["trend:trend_strong", "volatility:volatility_low"], "buy", 0.9
        )
        self.fuzzy_system.add_rule(
            ["trend:trend_weak", "volatility:volatility_high"], "sell", 0.9
        )
        self.fuzzy_system.add_rule(
            ["trend:trend_neutral", "volatility:volatility_medium"], "hold", 0.8
        )
        self.fuzzy_system.add_rule(
            ["trend:trend_strong", "volatility:volatility_medium"], "buy", 0.7
        )
        self.fuzzy_system.add_rule(
            ["trend:trend_weak", "volatility:volatility_low"], "sell", 0.6
        )

    def _update_indicators(self, bar: BarData):
        """更新技术指标"""
        # 更新Hull MA
        if self.hull_ma:
            hull_value = self.hull_ma.update(bar.close)
            if hull_value is not None:
                self.state.hull_ma = hull_value

        # 更新STC
        if self.stc:
            stc_value, stc_signal = self.stc.update(bar.high, bar.low, bar.close)
            if stc_value is not None and stc_signal is not None:
                self.state.stc_value = stc_value
                self.state.stc_signal = stc_signal

    def _generate_signal(self, bar: BarData) -> float:
        """生成交易信号"""
        # 技术指标信号
        indicator_signal = 0.0

        # Hull MA信号
        if self.state.hull_ma > 0 and bar.close > self.state.hull_ma:
            indicator_signal += 0.3
        elif self.state.hull_ma > 0 and bar.close < self.state.hull_ma:
            indicator_signal -= 0.3

        # STC信号
        if self.state.stc_value > 0 and self.state.stc_signal > 0:
            if self.state.stc_value > self.state.stc_signal:
                indicator_signal += 0.4
            else:
                indicator_signal -= 0.4

        # 模糊推理信号
        fuzzy_signal = 0.0
        if self.params.enable_fuzzy and self.fuzzy_system:
            # 准备模糊输入
            trend_strength = indicator_signal  # 使用指标信号作为趋势强度
            volatility = self.state.volatility

            # 模糊推理
            inputs = {
                'trend': trend_strength,
                'volatility': volatility
            }

            outputs = self.fuzzy_system.infer(inputs)
            fuzzy_signal = self.fuzzy_system.defuzzify(outputs)
            self.state.fuzzy_signal = fuzzy_signal

        # 综合信号
        final_signal = indicator_signal * 0.6 + fuzzy_signal * 0.4
        self.state.final_signal = final_signal
        self.state.signal_strength = abs(final_signal)

        return final_signal

    def _execute_trade(self, signal: float, bar: BarData):
        """执行交易"""
        # 检查是否需要止损或止盈
        if self.state.position > 0:
            if bar.close <= self.stop_loss_price:
                self._close_position("止损")
                return
            elif bar.close >= self.take_profit_price:
                self._close_position("止盈")
                return

        # 生成交易信号
        if abs(signal) > self.params.signal_threshold:
            # 多头信号
            if signal > 0 and self.state.position <= 0:
                if self.state.position < 0:
                    self._close_position("平空仓")

                if self.state.position < self.params.max_position:
                    self._open_position("开多仓", "buy")

            # 空头信号
            elif signal < 0 and self.state.position >= 0:
                if self.state.position > 0:
                    self._close_position("平多仓")

                if abs(self.state.position) < self.params.max_position:
                    self._open_position("开空仓", "sell")

    def _open_position(self, reason: str, direction: str):
        """开仓"""
        if self.params.symbol == "":
            self.write_log("未设置合约代码，无法开仓")
            return

        price = self._get_price(direction)

        order_id = self.send_order(
            symbol=self.params.symbol,
            exchange=self.params.exchange,
            direction=direction,
            offset="open",
            volume=self.params.fixed_size,
            price=price,
            type="limit"
        )

        self.write_log(f"{reason}: {direction} {self.params.fixed_size}手 @ {price}, 订单ID: {order_id}")

    def _close_position(self, reason: str):
        """平仓"""
        if self.params.symbol == "" or self.state.position == 0:
            return

        direction = "sell" if self.state.position > 0 else "buy"
        volume = abs(self.state.position)

        price = self._get_price(direction)

        order_id = self.send_order(
            symbol=self.params.symbol,
            exchange=self.params.exchange,
            direction=direction,
            offset="close",
            volume=volume,
            price=price,
            type="limit"
        )

        self.write_log(f"{reason}: {direction} {volume}手 @ {price}, 订单ID: {order_id}")

    def _get_price(self, direction: str) -> float:
        """获取委托价格"""
        if not self.price_history:
            return 0.0

        last_price = self.price_history[-1]

        if direction == "buy":
            return last_price + self.params.slippage
        else:
            return last_price - self.params.slippage

    def _update_state(self, bar: BarData):
        """更新状态"""
        # 更新趋势强度
        if len(self.price_history) >= 5:
            recent_prices = list(self.price_history)[-5:]
            returns = [(recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1] 
                       for i in range(1, len(recent_prices))]
            self.state.trend_strength = float(np.mean(returns))

        # 更新市场状态
        if self.state.volatility < 0.01:
            self.state.market_regime = "low_volatility"
        elif self.state.volatility > 0.03:
            self.state.market_regime = "high_volatility"
        else:
            self.state.market_regime = "normal"

        # 更新未实现盈亏
        if self.state.position != 0 and self.state.entry_price > 0:
            self.state.unrealized_pnl = (bar.close - self.state.entry_price) * self.state.position

        # 更新最后信号价格
        if abs(self.state.final_signal) > self.params.signal_threshold:
            self.state.last_signal_price = bar.close

    def _reset_state(self):
        """重置状态"""
        self.state.position = 0
        self.state.entry_price = 0.0
        self.state.unrealized_pnl = 0.0
        self.state.last_signal_price = 0.0
        self.state.total_trades = 0
        self.state.winning_trades = 0
        self.state.total_profit = 0.0
        self.state.win_rate = 0.0
        self.state.hull_ma = 0.0
        self.state.stc_value = 50.0
        self.state.stc_signal = 50.0
        self.state.fuzzy_signal = 0.0
        self.state.final_signal = 0.0
        self.state.signal_strength = 0.0
        self.state.volatility = 0.0
        self.state.trend_strength = 0.0
        self.state.market_regime = "normal"

        self.last_signal = 0
        self.entry_time = 0
        self.stop_loss_price = 0
        self.take_profit_price = 0

        self.price_history.clear()
        self.volatility_history.clear()
