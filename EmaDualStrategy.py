from typing import Literal

import numpy as np

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TradeData, TickData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K 线周期")
    trade_direction: Literal["buy", "sell", "auto"] = Field(default="buy", title="交易方向")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    max_positions: int = Field(default=10, title="最大持仓数")
    fast_ema_period: int = Field(default=5, title="快线EMA周期")
    slow_sma_period: int = Field(default=20, title="慢线SMA周期")
    volume_threshold_multiplier: float = Field(default=1.5, title="成交量突破阈值倍数")
    atr_period: int = Field(default=14, title="ATR周期")
    atr_multiplier: float = Field(default=1.5, title="ATR倍数用于追踪止盈止损")
    # 趋势判断参数
    min_trend_angle_deg: float = Field(default=15.0, title="最小趋势角度(度)")


class State(BaseState):
    """状态映射模型"""
    fast_ema: float = Field(default=0, title="快线EMA")
    slow_sma: float = Field(default=0, title="慢线SMA")
    volume_avg: float = Field(default=0, title="平均成交量")
    atr: float = Field(default=0, title="ATR")
    trailing_stop_price: float = Field(default=0, title="追踪止损价格")
    take_profit_price: float = Field(default=0, title="止盈价格")
    # 趋势判断相关状态
    sma_slope_deg: float = Field(default=0, title="SMA斜率角度(度)")


class EmaDualStrategy(BaseStrategy):
    """双EMA趋势跟踪策略 - 快线EMA用于信号点, 慢线SMA用于趋势方向"""
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        """参数表"""

        self.state_map = State()
        """状态表"""

        self.buy_signal: bool = False
        self.sell_signal: bool = False
        self.cover_signal: bool = False
        self.short_signal: bool = False

        self.tick: TickData = None

        self.fast_ema_prev = 0
        self.slow_sma_prev = 0
        self.volume_avg_prev = 0
        
        self.order_id = None
        self.signal_price = 0

        # 追踪止盈止损相关
        self.highest_high_since_entry = 0  # 多单入场后的最高价
        self.lowest_low_since_entry = float('inf')  # 空单入场后的最低价
        self.entry_price = 0  # 入场价格

    def is_market_trending(self) -> bool:
        """
        判断市场是否处于趋势行情中
        返回True表示是趋势行情，返回False表示不是
        """
        return abs(self.state_map.sma_slope_deg) >= self.params_map.min_trend_angle_deg

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标"""
        return {
            f"EMA{self.params_map.fast_ema_period}": self.state_map.fast_ema,
            f"SMA{self.params_map.slow_sma_period}": self.state_map.slow_sma,
        }

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标"""
        return {
            "Volume_Avg": self.state_map.volume_avg,
            "ATR": self.state_map.atr,
            "SMA_Angle": self.state_map.sma_slope_deg,
        }

    def on_tick(self, tick: TickData):
        super().on_tick(tick)

        self.tick = tick

        self.kline_generator.tick_to_kline(tick)

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        super().on_trade(trade, log)
        # 更新入场价格和极值
        if trade.direction == "buy" and trade.offset == "open":
            self.entry_price = trade.price
            self.highest_high_since_entry = trade.price
            self.lowest_low_since_entry = float('inf')
        elif trade.direction == "sell" and trade.offset == "open":
            self.entry_price = trade.price
            self.lowest_low_since_entry = trade.price
            self.highest_high_since_entry = 0
        self.order_id = None

    def on_start(self):
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()

        super().on_start()

        self.signal_price = 0

        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False

        self.tick = None

        self.update_status_bar()

    def on_stop(self):
        super().on_stop()

    def callback(self, kline: KLineData) -> None:
        """接受 K 线回调"""
        # 计算指标
        self.calc_indicator()

        # 计算信号
        self.calc_signal(kline)

        # 信号执行
        self.exec_signal()

        # 线图更新
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        if self.trading:
            self.update_status_bar()

    def real_time_callback(self, kline: KLineData) -> None:
        """使用收到的实时推送 K 线来计算指标并更新线图"""
        self.calc_indicator()

        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        self.update_status_bar()

    def calc_indicator(self) -> None:
        """计算所有技术指标"""
        # 计算快线EMA
        fast_ema_array = self.kline_generator.producer.ema(self.params_map.fast_ema_period, array=True)
        self.fast_ema_prev, self.state_map.fast_ema = np.round(fast_ema_array[-2:], 2)
        
        # 计算慢线SMA
        slow_sma_array = self.kline_generator.producer.sma(self.params_map.slow_sma_period, array=True)
        self.slow_sma_prev, self.state_map.slow_sma = np.round(slow_sma_array[-2:], 2)
        
        # 计算平均成交量 (使用20周期简单移动平均)
        volume_array = self.kline_generator.producer.volume
        volume_avg_array = self.kline_generator.producer.sma(20, array=True, price=volume_array)
        self.volume_avg_prev, self.state_map.volume_avg = np.round(volume_avg_array[-2:], 2)
        
        # 计算ATR
        self.state_map.atr = round(self.kline_generator.producer.atr(self.params_map.atr_period)[0], 2)
        
        # 计算SMA斜率角度
        if self.slow_sma_prev != 0:
            # 计算斜率 (rise/run)，这里run为1个周期
            slope = (self.state_map.slow_sma - self.slow_sma_prev) / self.slow_sma_prev
            # 将斜率转换为角度（度）
            self.state_map.sma_slope_deg = np.degrees(np.arctan(slope))
        else:
            self.state_map.sma_slope_deg = 0.0

    def calc_signal(self, kline: KLineData):
        """计算交易信号"""
        # 基本趋势判断: 慢线SMA方向
        long_trend = self.state_map.sma_slope_deg >= self.params_map.min_trend_angle_deg
        short_trend = self.state_map.sma_slope_deg <= -self.params_map.min_trend_angle_deg
        
        # 信号点判断: 快线EMA与慢线SMA交叉
        # 金叉: 快线上穿慢线
        golden_cross = self.fast_ema_prev <= self.slow_sma_prev and self.state_map.fast_ema > self.state_map.slow_sma
        # 死叉: 快线下穿慢线
        death_cross = self.fast_ema_prev >= self.slow_sma_prev and self.state_map.fast_ema < self.state_map.slow_sma
        
        # 成交量突破判断
        volume_breakout = kline.volume > self.state_map.volume_avg * self.params_map.volume_threshold_multiplier
        
        # 综合信号生成
        # 多单开仓: 上升趋势 + 金叉 + 成交量突破
        self.buy_signal = long_trend and golden_cross and volume_breakout
        # 空单开仓: 下降趋势 + 死叉 + 成交量突破
        self.short_signal = short_trend and death_cross and volume_breakout
        
        # 平仓信号与开仓信号相反 (这部分不受趋势角度限制)
        self.sell_signal = short_trend and death_cross
        self.cover_signal = long_trend and golden_cross

        # 根据交易方向调整信号
        if self.params_map.trade_direction == "buy":
            self.buy_signal, self.short_signal = False, self.buy_signal
            self.cover_signal, self.sell_signal = False, self.sell_signal
        elif self.params_map.trade_direction == "sell":
            self.buy_signal, self.short_signal = self.short_signal, False
            self.cover_signal, self.sell_signal = self.sell_signal, False
        # "auto"模式下不调整信号，直接使用原始计算结果

        # --- 新增的严格趋势过滤逻辑 ---
        # 只有在趋势行情中才允许开仓
        if not self.is_market_trending():
            self.buy_signal = False
            self.short_signal = False
        else:
            # 在趋势行情中，进一步限制逆势开仓
            # 1. 在SMA上升趋势中 (long_trend is True)
            if long_trend:
                # 禁止在上升趋势中开空单 (即使有死叉信号)
                self.short_signal = False
            # 2. 在SMA下降趋势中 (short_trend is True)
            elif short_trend:
                # 禁止在下降趋势中开多单 (即使有金叉信号)
                self.buy_signal = False
        # -------------------------------

        # 设置交易价格
        self.long_price = self.short_price = kline.close

        if self.tick:
            self.long_price = self.tick.ask_price1
            self.short_price = self.tick.bid_price1

            if self.params_map.price_type == "D2":
                self.long_price = self.tick.ask_price2
                self.short_price = self.tick.bid_price2

    def exec_signal(self):
        """执行交易信号"""
        self.signal_price = 0

        position = self.get_position(self.params_map.instrument_id)
        
        # 检查是否超过最大持仓数
        if abs(position.net_position) >= self.params_map.max_positions:
            # 如果已达到最大持仓数，则不执行任何开仓信号
            self.buy_signal = False
            self.short_signal = False
            
        # 检查是否试图建立对冲仓位（同时持有多空）
        if position.net_position > 0 and self.short_signal:
            # 已持有多单，禁止开空单
            self.short_signal = False
        elif position.net_position < 0 and self.buy_signal:
            # 已持有空单，禁止开多单
            self.buy_signal = False

        # 如果有挂单未成交，则取消
        if self.order_id is not None:
            self.cancel_order(self.order_id)

        # 检查是否需要更新追踪止盈止损
        if position.net_position > 0:
            # 更新多单的最高价
            if self.tick and self.tick.last_price > self.highest_high_since_entry:
                self.highest_high_since_entry = self.tick.last_price
                # 更新追踪止损价格
                self.state_map.trailing_stop_price = self.highest_high_since_entry - self.state_map.atr * self.params_map.atr_multiplier
                # 更新止盈价格 (例如设置为入场价加上两倍ATR)
                self.state_map.take_profit_price = self.entry_price + 2 * self.state_map.atr * self.params_map.atr_multiplier
        elif position.net_position < 0:
            # 更新空单的最低价
            if self.tick and self.tick.last_price < self.lowest_low_since_entry:
                self.lowest_low_since_entry = self.tick.last_price
                # 更新追踪止盈价格
                self.state_map.take_profit_price = self.lowest_low_since_entry + self.state_map.atr * self.params_map.atr_multiplier
                # 更新追踪止损价格 (例如设置为止盈价减去两倍ATR)
                self.state_map.trailing_stop_price = self.entry_price - 2 * self.state_map.atr * self.params_map.atr_multiplier

        # 检查追踪止盈止损条件
        if position.net_position > 0:
            # 多单止盈或止损
            if (self.tick and 
                (self.tick.last_price <= self.state_map.trailing_stop_price or 
                 self.tick.last_price >= self.state_map.take_profit_price)):
                self.signal_price = -self.short_price
                if self.trading:
                    self.order_id = self.auto_close_position(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        price=self.short_price,
                        volume=position.net_position,
                        order_direction="sell"
                    )
                # 追踪止盈止损触发后，重置信号标志，防止EMA信号立即反手
                self.buy_signal = False
                self.sell_signal = False
                self.cover_signal = False
                self.short_signal = False
        elif position.net_position < 0:
            # 空单止盈或止损
            if (self.tick and 
                (self.tick.last_price >= self.state_map.trailing_stop_price or 
                 self.tick.last_price <= self.state_map.take_profit_price)):
                self.signal_price = self.long_price
                if self.trading:
                    self.order_id = self.auto_close_position(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        price=self.long_price,
                        volume=abs(position.net_position),
                        order_direction="buy"
                    )
                # 追踪止盈止损触发后，重置信号标志，防止EMA信号立即反手
                self.buy_signal = False
                self.sell_signal = False
                self.cover_signal = False
                self.short_signal = False

        # 平仓信号处理 (优先级低于追踪止盈止损)
        if position.net_position > 0 and self.sell_signal:
            self.signal_price = -self.short_price
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.short_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
            # EMA平仓信号触发后，重置信号标志，防止立即反手
            self.buy_signal = False
            self.sell_signal = False
            self.cover_signal = False
            self.short_signal = False
        elif position.net_position < 0 and self.cover_signal:
            self.signal_price = self.long_price
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=abs(position.net_position),
                    order_direction="buy"
                )
            # EMA平仓信号触发后，重置信号标志，防止立即反手
            self.buy_signal = False
            self.sell_signal = False
            self.cover_signal = False
            self.short_signal = False

        # 开仓信号处理 (优先级低于追踪止盈止损和平仓信号)
        # 注意：严格的趋势和逆势开仓过滤已在calc_signal中完成
        
        if self.short_signal:
            self.signal_price = -self.short_price
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.short_price,
                    order_direction="sell"
                )
            # 开仓后重置信号标志，防止立即重复开仓
            self.buy_signal = False
            self.sell_signal = False
            self.cover_signal = False
            self.short_signal = False
        elif self.buy_signal:
            self.signal_price = self.long_price
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.long_price,
                    order_direction="buy"
                )
            # 开仓后重置信号标志，防止立即重复开仓
            self.buy_signal = False
            self.sell_signal = False
            self.cover_signal = False
            self.short_signal = False