#!/usr/bin/env python3
"""
高级交易策略演示脚本
展示如何使用AdvancedTradingStrategy进行交易和回测
"""

import sys
import os
import numpy as np
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from AdvancedTradingStrategy import AdvancedTradingStrategy, MarketRegime, SignalStrength
    from StrategyTester import StrategyTester, StrategyComparison
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保AdvancedTradingStrategy.py和StrategyTester.py在同一目录下")
    sys.exit(1)


def demo_basic_usage():
    """演示基本使用方法"""
    print("=" * 60)
    print("1. 基本策略使用演示")
    print("=" * 60)
    
    # 创建策略实例
    strategy = AdvancedTradingStrategy()
    
    # 设置策略参数
    strategy.params_map.exchange = "SHFE"
    strategy.params_map.instrument_id = "rb2501"
    strategy.params_map.kline_style = "M1"
    strategy.params_map.trend_threshold = 0.02
    strategy.params_map.risk_per_trade = 0.02
    strategy.params_map.max_position_size = 5
    strategy.params_map.atr_multiplier = 2.0
    
    print(f"策略参数设置:")
    print(f"  交易所: {strategy.params_map.exchange}")
    print(f"  合约: {strategy.params_map.instrument_id}")
    print(f"  K线周期: {strategy.params_map.kline_style}")
    print(f"  趋势阈值: {strategy.params_map.trend_threshold}")
    print(f"  单笔风险: {strategy.params_map.risk_per_trade:.1%}")
    print(f"  最大仓位: {strategy.params_map.max_position_size}")
    
    # 获取策略状态
    status = strategy.get_strategy_status()
    print(f"\n当前策略状态:")
    for key, value in status.items():
        print(f"  {key}: {value}")
    
    return strategy


def demo_backtest():
    """演示回测功能"""
    print("\n" + "=" * 60)
    print("2. 策略回测演示")
    print("=" * 60)
    
    # 创建策略
    strategy = AdvancedTradingStrategy()
    
    # 设置参数
    strategy.params_map.exchange = "SHFE"
    strategy.params_map.instrument_id = "rb2501"
    strategy.params_map.trend_threshold = 0.02
    strategy.params_map.risk_per_trade = 0.02
    strategy.params_map.atr_multiplier = 2.0
    
    # 创建测试器
    tester = StrategyTester(strategy)
    
    print("正在生成测试数据...")
    tester.load_test_data(generate_synthetic=True)
    print(f"测试数据生成完成，共 {len(tester.test_data)} 条记录")
    
    print("\n正在运行回测...")
    result = tester.run_backtest(start_capital=100000)
    
    print("\n回测结果:")
    print(f"  总收益率: {result.total_return:.2%}")
    print(f"  夏普比率: {result.sharpe_ratio:.2f}")
    print(f"  最大回撤: {result.max_drawdown:.2%}")
    print(f"  胜率: {result.win_rate:.2%}")
    print(f"  盈亏比: {result.profit_factor:.2f}")
    print(f"  总交易次数: {result.total_trades}")
    print(f"  波动率: {result.volatility:.2%}")
    print(f"  卡尔玛比率: {result.calmar_ratio:.2f}")
    
    # 生成报告
    report_filename = f"backtest_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    report = tester.generate_report(result, report_filename)
    print(f"\n详细报告已保存到: {report_filename}")
    
    return result, tester


def demo_parameter_optimization():
    """演示参数优化"""
    print("\n" + "=" * 60)
    print("3. 参数优化演示")
    print("=" * 60)
    
    # 创建策略
    strategy = AdvancedTradingStrategy()
    strategy.params_map.exchange = "SHFE"
    strategy.params_map.instrument_id = "rb2501"
    
    # 创建测试器
    tester = StrategyTester(strategy)
    tester.load_test_data(generate_synthetic=True)
    
    # 定义参数优化范围
    param_ranges = {
        'trend_threshold': [0.015, 0.02, 0.025],
        'risk_per_trade': [0.015, 0.02, 0.025],
        'atr_multiplier': [1.5, 2.0, 2.5]
    }
    
    print("参数优化范围:")
    for param, values in param_ranges.items():
        print(f"  {param}: {values}")
    
    print("\n正在进行参数优化...")
    optimization_result = tester.optimize_parameters(param_ranges)
    
    print("\n优化结果:")
    print(f"  最优参数: {optimization_result['best_params']}")
    print(f"  最优评分: {optimization_result['best_score']:.4f}")
    
    # 显示前3个最佳结果
    sorted_results = sorted(optimization_result['all_results'], 
                          key=lambda x: x['score'], reverse=True)
    
    print("\n前3个最佳参数组合:")
    for i, result in enumerate(sorted_results[:3]):
        print(f"  第{i+1}名:")
        print(f"    参数: {result['params']}")
        print(f"    评分: {result['score']:.4f}")
        print(f"    收益率: {result['result'].total_return:.2%}")
        print(f"    夏普比率: {result['result'].sharpe_ratio:.2f}")
        print(f"    最大回撤: {result['result'].max_drawdown:.2%}")
    
    return optimization_result


def demo_market_regime_analysis():
    """演示市场状态分析"""
    print("\n" + "=" * 60)
    print("4. 市场状态分析演示")
    print("=" * 60)
    
    strategy = AdvancedTradingStrategy()
    
    # 模拟不同市场状态的价格数据
    scenarios = {
        "上涨趋势": np.cumsum(np.random.normal(0.001, 0.01, 100)) + 4000,
        "下跌趋势": np.cumsum(np.random.normal(-0.001, 0.01, 100)) + 4000,
        "震荡行情": 4000 + 50 * np.sin(np.linspace(0, 4*np.pi, 100)) + np.random.normal(0, 10, 100)
    }
    
    for scenario_name, prices in scenarios.items():
        print(f"\n{scenario_name}场景分析:")
        
        # 清空历史数据
        strategy.price_history.clear()
        
        # 添加价格数据
        for i, price in enumerate(prices):
            price_data = {
                'timestamp': i,
                'open': price,
                'high': price * 1.005,
                'low': price * 0.995,
                'close': price,
                'volume': 1000
            }
            strategy.price_history.append(price_data)
        
        # 计算指标和识别市场状态
        if len(strategy.price_history) >= 50:
            strategy._calculate_indicators()
            strategy._identify_market_regime()
            
            print(f"  识别的市场状态: {strategy.state_map.market_regime}")
            print(f"  趋势强度: {strategy.state_map.trend_strength:.2f}")
            print(f"  波动率: {strategy.state_map.volatility:.4f}")
            print(f"  RSI: {strategy.state_map.rsi:.2f}")
            print(f"  MACD: {strategy.state_map.macd:.4f}")


def demo_signal_generation():
    """演示信号生成"""
    print("\n" + "=" * 60)
    print("5. 交易信号生成演示")
    print("=" * 60)
    
    strategy = AdvancedTradingStrategy()
    
    # 生成测试数据
    np.random.seed(42)
    base_price = 4000
    prices = base_price + np.cumsum(np.random.normal(0, 1, 200))
    
    # 添加价格历史
    for i, price in enumerate(prices):
        price_data = {
            'timestamp': i,
            'open': price,
            'high': price * 1.002,
            'low': price * 0.998,
            'close': price,
            'volume': 1000
        }
        strategy.price_history.append(price_data)
    
    # 计算指标
    strategy._calculate_indicators()
    strategy._identify_market_regime()
    
    # 创建模拟K线
    class MockKLine:
        def __init__(self, price):
            self.timestamp = len(strategy.price_history)
            self.close = price
            self.open = price
            self.high = price * 1.002
            self.low = price * 0.998
    
    # 生成信号
    kline = MockKLine(prices[-1])
    signal = strategy._generate_trading_signal(kline)
    
    if signal:
        print(f"生成交易信号:")
        print(f"  方向: {signal.direction}")
        print(f"  强度: {signal.strength.name} ({signal.strength.value})")
        print(f"  置信度: {signal.confidence:.2%}")
        print(f"  价格: {signal.price:.2f}")
        print(f"  止损: {signal.stop_loss:.2f}")
        print(f"  止盈: {signal.take_profit:.2f}")
    else:
        print("当前市场条件下未生成交易信号")
    
    print(f"\n当前市场状态: {strategy.state_map.market_regime}")
    print(f"技术指标状态:")
    print(f"  RSI: {strategy.state_map.rsi:.2f}")
    print(f"  MACD: {strategy.state_map.macd:.4f}")
    print(f"  EMA快线: {strategy.state_map.ema_fast:.2f}")
    print(f"  EMA慢线: {strategy.state_map.ema_slow:.2f}")


def main():
    """主函数"""
    print("高级交易策略演示程序")
    print("基于DemoKC.py改进的全能交易策略")
    print("=" * 60)
    
    try:
        # 1. 基本使用演示
        strategy = demo_basic_usage()
        
        # 2. 回测演示
        result, tester = demo_backtest()
        
        # 3. 参数优化演示
        optimization_result = demo_parameter_optimization()
        
        # 4. 市场状态分析演示
        demo_market_regime_analysis()
        
        # 5. 信号生成演示
        demo_signal_generation()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        print("\n主要改进点总结:")
        print("1. ✅ 智能趋势识别 - 自动判断上涨/下跌/震荡行情")
        print("2. ✅ 多指标信号融合 - RSI、MACD、EMA、布林带等")
        print("3. ✅ 先进风险管理 - 动态止损、仓位控制、回撤限制")
        print("4. ✅ 机器学习增强 - 特征提取和预测框架")
        print("5. ✅ 性能监控分析 - 详细统计和可视化报告")
        print("6. ✅ 参数自动优化 - 网格搜索最佳参数组合")
        
        print(f"\n相比原DemoKC策略的提升:")
        print(f"- 从单一指标升级为多指标融合")
        print(f"- 从固定方向升级为自适应多空")
        print(f"- 从简单止损升级为智能风险管理")
        print(f"- 从基础监控升级为全面性能分析")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
